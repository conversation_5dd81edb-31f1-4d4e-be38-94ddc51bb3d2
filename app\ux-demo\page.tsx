"use client";

import * as React from "react";
import { EnhancedDashboard } from "@/components/enhanced-dashboard";
import { EnhancedButton } from "@/components/ui/enhanced-button";
import { EnhancedInput } from "@/components/ui/enhanced-input";
import {
  Enhanced<PERSON>ard,
  EnhancedCardContent,
  EnhancedCardHeader,
  EnhancedCardTitle,
} from "@/components/ui/enhanced-card";
import { SkeletonTable } from "@/components/ui/enhanced-skeleton";
import { ErrorState, EmptyState } from "@/components/ui/error-states";
import { SmartSearch } from "@/components/ui/smart-search";
import {
  HoverScale,
  Floating,
  Shake,
  SuccessCheckmark,
  Counter,
  ProgressRing,
} from "@/components/ui/micro-interactions";
import {
  FadeInView,
  StaggeredAnimation,
  SlideInView,
} from "@/components/ui/page-transitions";
import { EnhancedTooltip } from "@/components/ui/enhanced-tooltip";
import { TouchTarget } from "@/components/ui/accessibility-helpers";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ed<PERSON>pin<PERSON>,
  InlineLoading,
} from "@/components/ui/loading-states";
import { Search, Heart, Zap, Shield, Users, TrendingUp } from "lucide-react";

export default function UXDemoPage() {
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState(false);
  const [success, setSuccess] = React.useState(false);
  const [shake, setShake] = React.useState(false);
  const [progress, setProgress] = React.useState(0);

  React.useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => (prev >= 100 ? 0 : prev + 1));
    }, 100);
    return () => clearInterval(interval);
  }, []);

  const handleSearch = async (query: string) => {
    return [
      {
        id: "1",
        title: "Dashboard",
        description: "Financial overview",
        category: "Navigation",
        icon: <TrendingUp className="h-4 w-4" />,
      },
      {
        id: "2",
        title: "Transactions",
        description: "View all transactions",
        category: "Data",
        icon: <Search className="h-4 w-4" />,
      },
    ];
  };

  const breadcrumbItems = [
    { label: "Home", href: "/", tooltip: "Go to homepage" },
    { label: "UX Demo", tooltip: "Premium user experience showcase" },
  ];

  return (
    <EnhancedDashboard
      title="Premium UX Showcase"
      breadcrumbItems={breadcrumbItems}
      showSearch={true}
    >
      <div className="space-y-12 py-6">
        {/* Micro-Interactions Section */}
        <section className="px-4 lg:px-6">
          <FadeInView>
            <h2 className="text-3xl font-bold mb-8">
              Micro-Interactions & Animations
            </h2>
          </FadeInView>

          <StaggeredAnimation
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Hover Effects</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent className="space-y-4">
                <HoverScale>
                  <div className="p-4 bg-blue-100 rounded-lg text-center cursor-pointer">
                    Hover to scale
                  </div>
                </HoverScale>
                <Floating>
                  <div className="p-4 bg-green-100 rounded-lg text-center">
                    <Heart className="h-6 w-6 mx-auto text-red-500" />
                    Floating
                  </div>
                </Floating>
              </EnhancedCardContent>
            </EnhancedCard>

            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Button States</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent className="space-y-4">
                <EnhancedButton
                  onClick={() => setLoading(!loading)}
                  loading={loading}
                  loadingText="Processing..."
                  className="w-full"
                >
                  Toggle Loading
                </EnhancedButton>
                <EnhancedButton
                  variant="success"
                  success={success}
                  successText="Done!"
                  onClick={() => setSuccess(!success)}
                  className="w-full"
                >
                  Success Button
                </EnhancedButton>
              </EnhancedCardContent>
            </EnhancedCard>

            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Feedback</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent className="space-y-4">
                <Shake trigger={shake}>
                  <EnhancedButton
                    variant="destructive"
                    onClick={() => setShake(!shake)}
                    className="w-full"
                  >
                    Shake on Error
                  </EnhancedButton>
                </Shake>
                <div className="flex justify-center">
                  <SuccessCheckmark size={32} />
                </div>
              </EnhancedCardContent>
            </EnhancedCard>

            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Progress</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent className="space-y-4">
                <ProgressRing progress={progress} />
                <div className="text-center">
                  <Counter from={0} to={1234} duration={2} prefix="$" />
                </div>
              </EnhancedCardContent>
            </EnhancedCard>
          </StaggeredAnimation>
        </section>

        {/* Enhanced Inputs Section */}
        <section className="px-4 lg:px-6">
          <FadeInView>
            <h2 className="text-3xl font-bold mb-8">Enhanced Form Controls</h2>
          </FadeInView>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Input States</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent className="space-y-6">
                <EnhancedInput
                  label="Default Input"
                  placeholder="Enter text..."
                  hint="This is a helpful hint"
                  leftIcon={<Search className="h-4 w-4" />}
                />
                <EnhancedInput
                  label="Success State"
                  value="Valid input"
                  success="Looks good!"
                  variant="success"
                />
                <EnhancedInput
                  label="Error State"
                  value="Invalid input"
                  error="This field is required"
                  variant="error"
                />
                <EnhancedInput
                  label="Loading State"
                  placeholder="Processing..."
                  loading={true}
                />
              </EnhancedCardContent>
            </EnhancedCard>

            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Smart Search</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent>
                <SmartSearch
                  placeholder="Search anything..."
                  onSearch={handleSearch}
                  onSelect={(result) => {
                    /* Handle selection */
                  }}
                  showRecentSearches={true}
                />
              </EnhancedCardContent>
            </EnhancedCard>
          </div>
        </section>

        {/* Loading States Section */}
        <section className="px-4 lg:px-6">
          <FadeInView>
            <h2 className="text-3xl font-bold mb-8">
              Loading States & Skeletons
            </h2>
          </FadeInView>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Loading Spinners</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent className="space-y-6">
                <div className="flex items-center justify-around">
                  <LoadingSpinner size="sm" />
                  <LoadingSpinner size="md" variant="dots" />
                  <LoadingSpinner size="lg" variant="pulse" />
                </div>
                <BrandedSpinner message="Loading your data..." />
                <InlineLoading message="Syncing..." />
              </EnhancedCardContent>
            </EnhancedCard>

            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Skeleton Screens</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent>
                <SkeletonTable rows={3} />
              </EnhancedCardContent>
            </EnhancedCard>
          </div>
        </section>

        {/* Error States Section */}
        <section className="px-4 lg:px-6">
          <FadeInView>
            <h2 className="text-3xl font-bold mb-8">
              Error Handling & Edge Cases
            </h2>
          </FadeInView>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <ErrorState
              title="Connection Error"
              description="Unable to connect to the server. Please check your internet connection."
              action={{
                label: "Retry",
                onClick: () => {
                  /* Handle retry */
                },
              }}
              secondaryAction={{
                label: "Go Back",
                onClick: () => {
                  /* Handle go back */
                },
              }}
            />

            <EmptyState
              title="No transactions found"
              description="You haven't added any transactions yet. Start by adding your first transaction."
              action={{
                label: "Add Transaction",
                onClick: () => {
                  /* Handle add transaction */
                },
              }}
              icon={<TrendingUp className="h-12 w-12 text-muted-foreground" />}
            />
          </div>
        </section>

        {/* Accessibility Features */}
        <section className="px-4 lg:px-6">
          <FadeInView>
            <h2 className="text-3xl font-bold mb-8">
              Accessibility & Touch Targets
            </h2>
          </FadeInView>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Touch Targets</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent className="space-y-4">
                <TouchTarget>
                  <button className="w-full p-2 bg-blue-100 rounded text-sm">
                    44px minimum
                  </button>
                </TouchTarget>
                <TouchTarget>
                  <button className="w-full p-3 bg-green-100 rounded">
                    Comfortable touch
                  </button>
                </TouchTarget>
              </EnhancedCardContent>
            </EnhancedCard>

            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Tooltips</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent className="space-y-4">
                <EnhancedTooltip content="This is a helpful tooltip">
                  <button className="p-2 bg-gray-100 rounded">
                    Hover for tooltip
                  </button>
                </EnhancedTooltip>
                <EnhancedTooltip
                  content="Detailed information about this feature"
                  side="right"
                >
                  <button className="p-2 bg-purple-100 rounded">
                    Right tooltip
                  </button>
                </EnhancedTooltip>
              </EnhancedCardContent>
            </EnhancedCard>

            <EnhancedCard>
              <EnhancedCardHeader>
                <EnhancedCardTitle>Focus States</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent className="space-y-4">
                <button className="w-full p-2 bg-blue-500 text-white rounded focus:ring-2 focus:ring-blue-300 focus:outline-none">
                  Keyboard navigable
                </button>
                <button className="w-full p-2 bg-green-500 text-white rounded focus:ring-2 focus:ring-green-300 focus:outline-none">
                  Tab to focus
                </button>
              </EnhancedCardContent>
            </EnhancedCard>
          </div>
        </section>

        {/* Page Transitions */}
        <section className="px-4 lg:px-6">
          <FadeInView>
            <h2 className="text-3xl font-bold mb-8">
              Page Transitions & Animations
            </h2>
          </FadeInView>

          <div className="space-y-6">
            <SlideInView direction="left">
              <div className="p-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg">
                <h3 className="text-xl font-bold mb-2">Slide In from Left</h3>
                <p>This content slides in smoothly from the left side.</p>
              </div>
            </SlideInView>

            <SlideInView direction="right">
              <div className="p-6 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg">
                <h3 className="text-xl font-bold mb-2">Slide In from Right</h3>
                <p>This content slides in smoothly from the right side.</p>
              </div>
            </SlideInView>

            <FadeInView>
              <div className="p-6 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg">
                <h3 className="text-xl font-bold mb-2">Fade In View</h3>
                <p>This content fades in when it comes into view.</p>
              </div>
            </FadeInView>
          </div>
        </section>

        {/* Performance Features */}
        <section className="px-4 lg:px-6">
          <FadeInView>
            <h2 className="text-3xl font-bold mb-8">
              Performance Optimizations
            </h2>
          </FadeInView>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <EnhancedCard variant="elevated">
              <EnhancedCardHeader>
                <Zap className="h-8 w-8 text-yellow-500 mb-2" />
                <EnhancedCardTitle>Lazy Loading</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent>
                <p className="text-sm text-muted-foreground">
                  Components and images load only when needed, improving initial
                  page load times.
                </p>
              </EnhancedCardContent>
            </EnhancedCard>

            <EnhancedCard variant="elevated">
              <EnhancedCardHeader>
                <Shield className="h-8 w-8 text-green-500 mb-2" />
                <EnhancedCardTitle>Skeleton Screens</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent>
                <p className="text-sm text-muted-foreground">
                  Smooth loading states that maintain layout and provide visual
                  feedback.
                </p>
              </EnhancedCardContent>
            </EnhancedCard>

            <EnhancedCard variant="elevated">
              <EnhancedCardHeader>
                <Users className="h-8 w-8 text-blue-500 mb-2" />
                <EnhancedCardTitle>Optimized Rendering</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent>
                <p className="text-sm text-muted-foreground">
                  Efficient re-renders and memoization for smooth 60fps
                  interactions.
                </p>
              </EnhancedCardContent>
            </EnhancedCard>
          </div>
        </section>
      </div>
    </EnhancedDashboard>
  );
}
