/**
 * Security headers utility for API routes
 * Provides common security headers to protect against various attacks
 */

export interface SecurityHeadersOptions {
  allowOrigins?: string[];
  allowMethods?: string[];
  allowHeaders?: string[];
  maxAge?: number;
  allowCredentials?: boolean;
}

/**
 * Default security headers for API responses
 */
export const DEFAULT_SECURITY_HEADERS = {
  // Prevent XSS attacks
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",

  // Prevent information disclosure
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "X-Powered-By": "", // Remove X-Powered-By header

  // Content Security Policy (basic)
  "Content-Security-Policy":
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self'; object-src 'none'; child-src 'none'; worker-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self';",

  // HSTS (only in production with HTTPS)
  ...(process.env.NODE_ENV === "production" && {
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
  }),
};

/**
 * CORS headers for API routes
 */
export function getCorsHeaders(
  options: SecurityHeadersOptions = {},
): Record<string, string> {
  const {
    allowOrigins = ["http://localhost:3000", "https://localhost:3000"],
    allowMethods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowHeaders = ["Content-Type", "Authorization", "X-Requested-With"],
    maxAge = 86400, // 24 hours
    allowCredentials = true,
  } = options;

  return {
    "Access-Control-Allow-Origin": allowOrigins.join(", "),
    "Access-Control-Allow-Methods": allowMethods.join(", "),
    "Access-Control-Allow-Headers": allowHeaders.join(", "),
    "Access-Control-Max-Age": maxAge.toString(),
    "Access-Control-Allow-Credentials": allowCredentials.toString(),
  };
}

/**
 * Combines security headers with CORS headers
 */
export function getSecurityHeaders(
  corsOptions?: SecurityHeadersOptions,
): Record<string, string> {
  return {
    ...DEFAULT_SECURITY_HEADERS,
    ...getCorsHeaders(corsOptions),
  };
}

/**
 * Creates a Response with security headers
 */
export function createSecureResponse(
  body: unknown,
  options: {
    status?: number;
    headers?: Record<string, string>;
    corsOptions?: SecurityHeadersOptions;
  } = {},
): Response {
  const { status = 200, headers = {}, corsOptions } = options;

  const securityHeaders = getSecurityHeaders(corsOptions);

  return new Response(typeof body === "string" ? body : JSON.stringify(body), {
    status,
    headers: {
      "Content-Type": "application/json",
      ...securityHeaders,
      ...headers,
    },
  });
}

/**
 * Rate limiting utility (basic implementation)
 * In production, use Redis or a proper rate limiting service
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string,
  limit: number = 100,
  windowMs: number = 15 * 60 * 1000, // 15 minutes
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const windowStart = now - windowMs;

  // Clean up old entries
  for (const [key, value] of rateLimitMap.entries()) {
    if (value.resetTime < now) {
      rateLimitMap.delete(key);
    }
  }

  const current = rateLimitMap.get(identifier);

  if (!current || current.resetTime < now) {
    // First request or window expired
    rateLimitMap.set(identifier, {
      count: 1,
      resetTime: now + windowMs,
    });
    return {
      allowed: true,
      remaining: limit - 1,
      resetTime: now + windowMs,
    };
  }

  if (current.count >= limit) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: current.resetTime,
    };
  }

  current.count++;
  return {
    allowed: true,
    remaining: limit - current.count,
    resetTime: current.resetTime,
  };
}

/**
 * Input sanitization utility
 */
export function sanitizeInput(input: unknown): unknown {
  if (typeof input === "string") {
    // Remove potentially dangerous characters
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "") // Remove script tags
      .replace(/javascript:/gi, "") // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, "") // Remove event handlers
      .trim()
      .slice(0, 10000); // Limit length
  }

  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }

  if (typeof input === "object" && input !== null) {
    const sanitized: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[sanitizeInput(key) as string] = sanitizeInput(value);
    }
    return sanitized;
  }

  return input;
}

/**
 * Validates request origin
 */
export function validateOrigin(request: Request): boolean {
  const origin = request.headers.get("origin");
  const allowedOrigins = [
    "http://localhost:3000",
    "https://localhost:3000",
    process.env.NEXTAUTH_URL,
    process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
  ].filter(Boolean);

  if (!origin) {
    // Allow requests without origin (e.g., mobile apps, Postman)
    return true;
  }

  return allowedOrigins.some((allowed) => allowed && origin === allowed);
}

/**
 * Middleware wrapper for API routes with security
 */
export function withSecurity(
  handler: (req: Request) => Promise<Response>,
  options: {
    rateLimit?: { limit: number; windowMs: number };
    corsOptions?: SecurityHeadersOptions;
    requireAuth?: boolean;
  } = {},
) {
  return async (req: Request): Promise<Response> => {
    try {
      // Validate origin
      if (!validateOrigin(req)) {
        return createSecureResponse(
          { error: "Invalid origin" },
          { status: 403, corsOptions: options.corsOptions },
        );
      }

      // Rate limiting
      if (options.rateLimit) {
        const clientIP = req.headers.get("x-forwarded-for") || "unknown";
        const rateCheck = checkRateLimit(
          clientIP,
          options.rateLimit.limit,
          options.rateLimit.windowMs,
        );

        if (!rateCheck.allowed) {
          return createSecureResponse(
            { error: "Rate limit exceeded" },
            {
              status: 429,
              headers: {
                "X-RateLimit-Limit": options.rateLimit.limit.toString(),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": new Date(
                  rateCheck.resetTime,
                ).toISOString(),
              },
              corsOptions: options.corsOptions,
            },
          );
        }
      }

      // Handle OPTIONS request for CORS
      if (req.method === "OPTIONS") {
        return createSecureResponse("", {
          status: 200,
          corsOptions: options.corsOptions,
        });
      }

      // Call the actual handler
      const response = await handler(req);

      // Add security headers to the response
      const securityHeaders = getSecurityHeaders(options.corsOptions);
      for (const [key, value] of Object.entries(securityHeaders)) {
        response.headers.set(key, value);
      }

      return response;
    } catch (error) {
      console.error("Security middleware error:", error);
      return createSecureResponse(
        { error: "Internal server error" },
        { status: 500, corsOptions: options.corsOptions },
      );
    }
  };
}
