"use client";

import * as React from "react";
import { AppSidebar } from "@/components/app-sidebar";
import { SiteHeader } from "@/components/site-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { EnhancedBreadcrumb } from "@/components/ui/breadcrumb-enhanced";
import {
  VisuallyHidden,
} from "@/components/ui/accessibility-helpers";
import { OfflineIndicator } from "@/components/ui/error-states";
import {
  PageTransition,
  FadeInView,
  StaggeredAnimation,
} from "@/components/ui/page-transitions";
import { SmartSearch } from "@/components/ui/smart-search";
import { EnhancedTooltip } from "@/components/ui/enhanced-tooltip";
import { Search, Settings, HelpCircle } from "lucide-react";
import { useRouter } from "next/navigation";

import { SearchResult } from "@/components/ui/smart-search";

interface EnhancedDashboardProps {
  children: React.ReactNode;
  title?: string;
  breadcrumbItems?: Array<{
    label: string;
    href?: string;
    tooltip?: string;
  }>;
  showSearch?: boolean;
  className?: string;
}

export function EnhancedDashboard({
  children,
  title,
  breadcrumbItems,
  showSearch = true,
  className,
}: EnhancedDashboardProps) {
  const router = useRouter();
  const [searchResults, setSearchResults] = React.useState<SearchResult[]>([]);

  // Keyboard shortcuts - moved to useEffect to avoid hook order issues
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 'k') {
        event.preventDefault();
        const searchInput = document.querySelector(
          '[data-slot="enhanced-input"]',
        ) as HTMLInputElement;
        searchInput?.focus();
      } else if (event.ctrlKey && event.key === ',') {
        event.preventDefault();
        router.push("/settings");
      } else if (event.ctrlKey && event.key === '?') {
        event.preventDefault();
        router.push("/help");
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [router]);

  const handleSearch = async (query: string) => {
    // Mock search implementation - replace with actual search logic
    const mockResults = [
      {
        id: "1",
        title: "Dashboard",
        description: "View your financial overview",
        category: "Navigation",
        url: "/dashboard",
        icon: <Search className="h-4 w-4" />,
      },
      {
        id: "2",
        title: "Transactions",
        description: "Manage your transactions",
        category: "Navigation",
        url: "/transactions",
        icon: <Search className="h-4 w-4" />,
      },
      {
        id: "3",
        title: "Settings",
        description: "Configure your preferences",
        category: "Navigation",
        url: "/settings",
        icon: <Settings className="h-4 w-4" />,
      },
    ].filter(
      (item) =>
        item.title.toLowerCase().includes(query.toLowerCase()) ||
        item.description.toLowerCase().includes(query.toLowerCase()),
    );

    return mockResults;
  };

  const handleSearchSelect = (result: SearchResult) => {
    if (result.url) {
      router.push(result.url);
    }
  };

  return (
    <>
      <OfflineIndicator />

      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />

        <SidebarInset>
          <SiteHeader />

          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              {/* Enhanced Header with Breadcrumbs and Search */}
              <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="flex h-14 items-center gap-4 px-4 lg:px-6">
                  <div className="flex-1">
                    {breadcrumbItems && (
                      <EnhancedBreadcrumb items={breadcrumbItems} />
                    )}
                    {title && !breadcrumbItems && (
                      <h1 className="text-lg font-semibold">{title}</h1>
                    )}
                  </div>

                  {showSearch && (
                    <div className="flex items-center gap-2">
                      <SmartSearch
                        placeholder="Search... (Ctrl+K)"
                        onSearch={handleSearch}
                        onSelect={handleSearchSelect}
                        className="w-64"
                      />

                      <EnhancedTooltip content="Settings (Ctrl+,)">
                        <button
                          onClick={() => router.push("/settings")}
                          className="p-2 hover:bg-accent rounded-md transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                          aria-label="Open settings"
                        >
                          <Settings className="h-4 w-4" />
                        </button>
                      </EnhancedTooltip>

                      <EnhancedTooltip content="Help (Ctrl+?)">
                        <button
                          onClick={() => router.push("/help")}
                          className="p-2 hover:bg-accent rounded-md transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                          aria-label="Open help"
                        >
                          <HelpCircle className="h-4 w-4" />
                        </button>
                      </EnhancedTooltip>
                    </div>
                  )}
                </div>
              </div>

              {/* Main Content with Transitions */}
              <main
                id="main-content"
                className={`flex-1 ${className || ""}`}
                role="main"
                aria-label={title || "Main content"}
              >
                <PageTransition variant="slideUp">{children}</PageTransition>
              </main>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>

      {/* Keyboard Shortcuts Help */}
      <VisuallyHidden>
        <div role="region" aria-label="Keyboard shortcuts">
          <p>Press Ctrl+K to search</p>
          <p>Press Ctrl+, to open settings</p>
          <p>Press Ctrl+? to open help</p>
        </div>
      </VisuallyHidden>
    </>
  );
}
