"use client";

import * as React from "react";
import { TrendingUp } from "lucide-react";
import { Pie, Pie<PERSON><PERSON> } from "recharts";
import { useAuth } from "@/lib/contexts/auth-context";
import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

// This will be populated with real data from user transactions
// TODO: Implement data fetching from getUserTransactions and group by category
const chartData: Array<{ category: string; amount: number; fill: string }> = [];

const chartConfig = {
  amount: {
    label: "Amount",
  },
  food: {
    label: "Food & Dining",
    color: "var(--chart-1)",
  },
  transport: {
    label: "Transportation",
    color: "var(--chart-2)",
  },
  shopping: {
    label: "Shopping",
    color: "var(--chart-3)",
  },
  utilities: {
    label: "Utilities",
    color: "var(--chart-4)",
  },
  entertainment: {
    label: "Entertainment",
    color: "var(--chart-5)",
  },
  health: {
    label: "Health",
    color: "var(--chart-1)",
  },
} satisfies ChartConfig;

export function SpendingCategoriesChart() {
  const { profile } = useAuth();
  const [loading, setLoading] = useState(true);

  const currency = profile?.currency_code || "KWD";

  useEffect(() => {
    // Simulate loading for realistic UX
    const timer = setTimeout(() => setLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const totalSpending = chartData.reduce((sum, item) => sum + item.amount, 0);
  const topCategory =
    chartData.length > 0
      ? chartData.reduce((prev, current) =>
          prev.amount > current.amount ? prev : current,
        )
      : null;

  if (loading) {
    return (
      <Card className="flex flex-col">
        <CardHeader className="items-center pb-0">
          <CardTitle>Spending by Category</CardTitle>
          <CardDescription>Loading spending data...</CardDescription>
        </CardHeader>
        <CardContent className="flex-1 pb-0">
          <div className="mx-auto aspect-square max-h-[250px] w-full animate-pulse bg-gray-200 rounded-full"></div>
        </CardContent>
      </Card>
    );
  }

  // Show empty state when no data is available
  if (chartData.length === 0) {
    return (
      <Card className="flex flex-col">
        <CardHeader className="items-center pb-0">
          <CardTitle>Spending by Category</CardTitle>
          <CardDescription>No spending data available</CardDescription>
        </CardHeader>
        <CardContent className="flex-1 pb-0">
          <div className="mx-auto aspect-square max-h-[250px] w-full flex items-center justify-center border-2 border-dashed border-gray-300 rounded-full">
            <div className="text-center text-gray-500">
              <div className="text-sm font-medium">No transactions yet</div>
              <div className="text-xs">
                Add some transactions to see your spending breakdown
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex-col gap-2 text-sm">
          <div className="text-muted-foreground leading-none text-center">
            Start tracking your expenses to see category breakdown
          </div>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>Spending by Category</CardTitle>
        <CardDescription>Current month breakdown</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={chartData}
              dataKey="amount"
              nameKey="category"
              stroke="0"
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 leading-none font-medium">
          {topCategory?.category} is your largest expense{" "}
          <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Total spending: {formatCurrency(totalSpending)} this month
        </div>
      </CardFooter>
    </Card>
  );
}
