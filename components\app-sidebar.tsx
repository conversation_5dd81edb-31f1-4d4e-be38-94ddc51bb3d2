"use client";

import * as React from "react";
import Link from "next/link";
import {
  IconDashboard,
  IconMessageCircle,
  IconReceipt,
  IconRepeat,
  IconTarget,
  IconCreditCard,
  IconSettings,
  IconHelp,
  IconWallet,
} from "@tabler/icons-react";

import { useAuth } from "@/lib/contexts/auth-context";
import { NavMain } from "@/components/nav-main";
import { NavSecondary } from "@/components/nav-secondary";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

const navMain = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: IconDashboard,
  },
  {
    title: "AI Chat",
    url: "/ai-chat",
    icon: IconMessageCircle,
  },
  {
    title: "Transactions",
    url: "/transactions",
    icon: IconReceipt,
  },
  {
    title: "Recurring Payments",
    url: "/recurring-payments",
    icon: IconRepeat,
  },
  {
    title: "Goals",
    url: "/goals",
    icon: IconTarget,
  },
  {
    title: "Loans & Debts",
    url: "/loans-debts",
    icon: IconCreditCard,
  },
];

const navSecondary = [
  {
    title: "Settings Hub",
    url: "/settings",
    icon: IconSettings,
  },
  {
    title: "Help & Support",
    url: "/help",
    icon: IconHelp,
  },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, profile } = useAuth();

  const userData = {
    name: profile?.full_name || user?.email || "User",
    email: user?.email || "",
    avatar: profile?.avatar_url || "",
  };
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link href="/dashboard">
                <IconWallet className="!size-5" />
                <span className="text-base font-semibold">Finance Tracker</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navMain} />
        <NavSecondary items={navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
    </Sidebar>
  );
}
