"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layouts/app-layout";
import { Goal } from "@/lib/types/database";
import { GoalForm } from "@/components/goals/goal-form";
import { GoalsTable } from "@/components/goals/goals-table";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Plus, Upload, Download } from "lucide-react";

export default function GoalsPage() {
  const [showForm, setShowForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | undefined>();
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleAddGoal = () => {
    setEditingGoal(undefined);
    setShowForm(true);
  };

  const handleEditGoal = (goal: Goal) => {
    setEditingGoal(goal);
    setShowForm(true);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingGoal(undefined);
    setRefreshTrigger((prev) => prev + 1);
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingGoal(undefined);
  };

  return (
    <AppLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Financial Goals
            </h1>
            <p className="text-muted-foreground">
              Set and track your savings goals to achieve your financial dreams
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Upload className="w-4 h-4 mr-2" />
              Import
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button onClick={handleAddGoal}>
              <Plus className="w-4 h-4 mr-2" />
              Create Goal
            </Button>
          </div>
        </div>

        {/* Goals Table */}
        <GoalsTable
          onEditGoal={handleEditGoal}
          onAddGoal={handleAddGoal}
          refreshTrigger={refreshTrigger}
        />

        {/* Goal Form Dialog */}
        <Dialog open={showForm} onOpenChange={setShowForm}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingGoal ? "Edit Goal" : "Create New Goal"}
              </DialogTitle>
            </DialogHeader>
            <GoalForm
              goal={editingGoal}
              onSuccess={handleFormSuccess}
              onCancel={handleFormCancel}
            />
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  );
}
