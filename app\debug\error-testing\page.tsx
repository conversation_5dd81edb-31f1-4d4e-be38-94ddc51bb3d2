"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ErrorBoundary } from "@/components/error-boundary";
import {
  NetworkError,
  DatabaseError,
  AuthError,
  TimeoutError,
  FormError,
  LoadingError,
} from "@/components/error/error-messages";
import { AlertTriangle, Bug, Clock, CheckCircle } from "lucide-react";

// Component that throws an error on demand
function ErrorThrower({ shouldThrow }: { shouldThrow: boolean }) {
  if (shouldThrow) {
    throw new Error(
      "This is a test error thrown by the ErrorThrower component!",
    );
  }
  return (
    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
      <div className="flex items-center gap-2 text-green-700">
        <CheckCircle className="w-4 h-4" />
        <span>Component is working normally - no error thrown</span>
      </div>
    </div>
  );
}

// Component that simulates async errors
function AsyncErrorThrower({ shouldThrow }: { shouldThrow: boolean }) {
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const simulateAsyncOperation = async () => {
    setLoading(true);
    setError(null);

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      if (shouldThrow) {
        throw new Error("Simulated async operation failed!");
      }

      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error");
      setLoading(false);
    }
  };

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center gap-2 text-red-700">
          <AlertTriangle className="w-4 h-4" />
          <span>Async Error: {error}</span>
        </div>
        <Button
          onClick={() => setError(null)}
          variant="outline"
          size="sm"
          className="mt-2"
        >
          Clear Error
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Button
        onClick={simulateAsyncOperation}
        disabled={loading}
        className="w-full"
      >
        {loading ? "Processing..." : "Test Async Operation"}
      </Button>
      {loading && (
        <div className="text-sm text-gray-600">
          Simulating async operation...
        </div>
      )}
    </div>
  );
}

export default function ErrorTestingPage() {
  const [throwError, setThrowError] = useState(false);
  const [throwAsyncError, setThrowAsyncError] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults((prev) => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${result}`,
    ]);
  };

  const testErrorBoundary = () => {
    setThrowError(true);
    addTestResult("Error boundary test triggered");
  };

  const resetErrorBoundary = () => {
    setThrowError(false);
    addTestResult("Error boundary reset");
  };

  const testAsyncError = () => {
    setThrowAsyncError(true);
    addTestResult("Async error test triggered");
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">Error Boundary Testing</h1>
        <p className="text-gray-600">
          Test error boundaries and error handling components to ensure they
          work correctly.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Error Boundary Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bug className="w-5 h-5" />
              Error Boundary Tests
            </CardTitle>
            <CardDescription>
              Test React error boundaries with intentional component errors
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Button
                onClick={testErrorBoundary}
                variant="destructive"
                className="w-full"
              >
                Trigger Component Error
              </Button>
              <Button
                onClick={resetErrorBoundary}
                variant="outline"
                className="w-full"
              >
                Reset Error State
              </Button>
            </div>

            <ErrorBoundary>
              <ErrorThrower shouldThrow={throwError} />
            </ErrorBoundary>

            <Alert>
              <AlertTriangle className="w-4 h-4" />
              <AlertDescription>
                <strong>Expected behavior:</strong> When you click "Trigger
                Component Error", the error boundary should catch the error and
                display a fallback UI with retry options.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Async Error Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Async Error Tests
            </CardTitle>
            <CardDescription>
              Test error handling for asynchronous operations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Button
                onClick={testAsyncError}
                variant="destructive"
                className="w-full"
              >
                Enable Async Error
              </Button>
              <Button
                onClick={() => setThrowAsyncError(false)}
                variant="outline"
                className="w-full"
              >
                Disable Async Error
              </Button>
            </div>

            <AsyncErrorThrower shouldThrow={throwAsyncError} />

            <Alert>
              <AlertTriangle className="w-4 h-4" />
              <AlertDescription>
                <strong>Expected behavior:</strong> When async error is enabled,
                the operation should fail and display an error message with
                recovery options.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Error Message Components */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Error Message Components</CardTitle>
            <CardDescription>
              Preview different types of error messages and their styling
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Network Error</h4>
                <NetworkError
                  onRetry={() => addTestResult("Network retry clicked")}
                />
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Database Error</h4>
                <DatabaseError
                  onRetry={() => addTestResult("Database retry clicked")}
                />
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Authentication Error</h4>
                <AuthError
                  onRetry={() => addTestResult("Auth retry clicked")}
                />
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Timeout Error</h4>
                <TimeoutError
                  onRetry={() => addTestResult("Timeout retry clicked")}
                />
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Form Validation Error</h4>
                <FormError
                  errors={[
                    "Email is required",
                    "Password must be at least 8 characters",
                    "Please accept the terms and conditions",
                  ]}
                />
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Loading Error</h4>
                <LoadingError
                  resource="user data"
                  onRetry={() => addTestResult("Loading retry clicked")}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Test Results Log</CardTitle>
            <CardDescription>
              Track your testing actions and verify error handling behavior
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <h4 className="font-medium">Actions Log:</h4>
                <Button onClick={clearResults} variant="outline" size="sm">
                  Clear Log
                </Button>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg max-h-40 overflow-y-auto">
                {testResults.length === 0 ? (
                  <p className="text-gray-500 text-sm">
                    No test actions yet. Start testing above!
                  </p>
                ) : (
                  <div className="space-y-1">
                    {testResults.map((result, index) => (
                      <div key={index} className="text-sm font-mono">
                        {result}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Testing Instructions */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">1. Error Boundary Test:</h4>
              <ul className="list-disc list-inside text-sm space-y-1 text-gray-600">
                <li>Click "Trigger Component Error" to test error boundary</li>
                <li>Verify that a fallback UI appears with error details</li>
                <li>Test the "Try Again" button to recover from the error</li>
                <li>Check that error details are shown in development mode</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">2. Async Error Test:</h4>
              <ul className="list-disc list-inside text-sm space-y-1 text-gray-600">
                <li>Enable async error and trigger the operation</li>
                <li>Verify that the error is caught and displayed properly</li>
                <li>Test the error recovery mechanism</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">3. Error Message Components:</h4>
              <ul className="list-disc list-inside text-sm space-y-1 text-gray-600">
                <li>
                  Review each error message type for clarity and usefulness
                </li>
                <li>Test retry buttons to ensure they trigger callbacks</li>
                <li>Verify that icons and styling are appropriate</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
