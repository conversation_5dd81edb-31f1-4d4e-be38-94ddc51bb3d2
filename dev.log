
> my-app@0.1.0 dev
> next dev

 ⚠ Warning: Found multiple lockfiles. Selecting /mnt/c/Users/<USER>/bun.lock.
   Consider removing the lockfiles at:
   * /mnt/c/Users/<USER>/Desktop/my-app/package-lock.json

   ▲ Next.js 15.4.4
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 15.1s
 ○ Compiling /middleware ...
 ✓ Compiled /middleware in 526ms (224 modules)
 ○ Compiling /dashboard ...
 ✓ Compiled /dashboard in 12.6s (2343 modules)
 GET /dashboard 200 in 15336ms
 ○ Compiling /favicon.ico ...
 ✓ Compiled /favicon.ico in 1011ms (1305 modules)
 GET /favicon.ico 200 in 1645ms
 GET /favicon.ico 200 in 116ms
 ○ Compiling /_not-found ...
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled /_not-found in 3.1s (2365 modules)
 GET /sw.js 404 in 3967ms
 GET /dashboard 200 in 2004ms
 GET /favicon.ico 200 in 126ms
 GET /favicon.ico 200 in 126ms
 GET /sw.js 404 in 69ms
 ○ Compiling /ai-chat ...
 ✓ Compiled /ai-chat in 2.5s (2898 modules)
 GET /ai-chat 200 in 3094ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET /dashboard 200 in 854ms
 GET /favicon.ico 200 in 159ms
 GET /favicon.ico 200 in 134ms
 GET /sw.js 404 in 51ms
 GET /ai-chat 200 in 86ms
 GET /dashboard 200 in 415ms
 GET /favicon.ico 200 in 202ms
 GET /favicon.ico 200 in 157ms
 GET /sw.js 404 in 88ms
 GET /sw.js 404 in 113ms
 GET /dashboard 200 in 543ms
 GET /favicon.ico 200 in 281ms
 GET /favicon.ico 200 in 110ms
 GET /sw.js 404 in 113ms
 GET /ai-chat 200 in 138ms
 GET /sw.js 404 in 124ms
 GET /dashboard 200 in 468ms
 GET /favicon.ico 200 in 211ms
 GET /favicon.ico 200 in 124ms
 GET /sw.js 404 in 71ms
 ○ Compiling /_error ...
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled /_error in 5s (2930 modules)
 GET /sw.js 404 in 7019ms
 GET /dashboard 200 in 3708ms
 GET /dashboard 200 in 3535ms
 GET /_next/static/css/app/layout.css?v=1753712662276 404 in 189ms
 GET /_next/static/chunks/main-app.js?v=1753712662276 404 in 246ms
 ✓ Compiled /favicon.ico in 494ms (1487 modules)
 GET /favicon.ico 200 in 906ms
 GET /sw.js 404 in 311ms
 GET /_next/static/css/app/layout.css?v=1753712697384 404 in 128ms
 GET /dashboard 200 in 782ms
 GET /_next/static/chunks/main-app.js?v=1753712697384 404 in 154ms
 GET /favicon.ico 200 in 55ms
 GET /sw.js 404 in 54ms
 GET /dashboard 404 in 84ms
 GET /favicon.ico 404 in 90ms
 GET /dashboard 404 in 107ms
 GET /dashboard 404 in 63ms
 GET /dashboard 404 in 53ms
 GET /dashboard 404 in 57ms
 GET /dashboard 404 in 56ms
 GET /dashboard 404 in 50ms
 GET /dashboard 404 in 54ms
 GET /dashboard 404 in 61ms
 GET /dashboard 404 in 65ms
 GET /dashboard 404 in 57ms
 GET /dashboard 404 in 55ms
 GET /dashboard 404 in 54ms
 GET /dashboard 404 in 57ms
 GET /dashboard 404 in 52ms
 GET /dashboard 404 in 50ms
 GET /dashboard 404 in 60ms
 GET /dashboard 404 in 48ms
 GET /dashboard 404 in 100ms
 GET /dashboard 404 in 70ms
 GET /dashboard 404 in 54ms
 GET /dashboard 404 in 55ms
 GET /dashboard 404 in 49ms
 GET /dashboard 404 in 78ms
 GET /dashboard 404 in 99ms
 GET /dashboard 404 in 48ms
 GET /dashboard 404 in 69ms
 GET /dashboard 404 in 84ms
 GET /dashboard 404 in 50ms
 GET /dashboard 404 in 53ms
 GET /dashboard 404 in 74ms
 GET /dashboard 404 in 50ms
 GET /dashboard 404 in 53ms
 GET /dashboard 404 in 52ms
 GET /dashboard 404 in 48ms
 GET /dashboard 404 in 47ms
 GET /dashboard 404 in 48ms
 GET /dashboard 404 in 60ms
 ○ Compiling /onboarding ...
 ✓ Compiled /onboarding in 2.4s (2890 modules)
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json',
  page: '/onboarding'
}
 ○ Compiling /_error ...
 ✓ Compiled /_error in 665ms (2977 modules)
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Users/<USER>/Desktop/my-app/.next/server/pages/_document.js'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Users/<USER>/Desktop/my-app/.next/server/pages/_document.js'
}
 GET /onboarding 500 in 9535ms
 ✓ Compiled /favicon.ico in 453ms (1520 modules)
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json',
  page: '/favicon.ico'
}
 GET /favicon.ico 500 in 986ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 931ms (2999 modules)
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json'
}
 GET /sw.js 500 in 1607ms
 GET /_next/static/webpack/78f55d67ab3ad387.webpack.hot-update.json 500 in 998ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json',
  page: '/onboarding'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Users/<USER>/Desktop/my-app/.next/server/pages/_document.js'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Users/<USER>/Desktop/my-app/.next/server/pages/_document.js'
}
 GET /onboarding 500 in 421ms
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json',
  page: '/favicon.ico'
}
 GET /favicon.ico 500 in 206ms
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Users/<USER>/Desktop/my-app/.next/routes-manifest.json'
}
 GET /sw.js 500 in 321ms
[?25h
