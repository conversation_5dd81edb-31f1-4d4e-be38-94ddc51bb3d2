"use client";

import * as React from "react";
import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  lazy,
  Suspense,
} from "react";
import { useAuth } from "@/lib/contexts/auth-context";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

// Lazy load heavy chart components to improve initial load performance
const LazyAreaChart = lazy(() =>
  import("recharts").then((module) => ({
    default: module.AreaChart,
  })),
);
const LazyArea = lazy(() =>
  import("recharts").then((module) => ({
    default: module.Area as any,
  })),
);
const LazyCartesianGrid = lazy(() =>
  import("recharts").then((module) => ({
    default: module.CartesianGrid,
  })),
);
const LazyXAxis = lazy(() =>
  import("recharts").then((module) => ({
    default: module.XAxis,
  })),
);
const LazyResponsiveContainer = lazy(() =>
  import("recharts").then((module) => ({
    default: module.ResponsiveContainer,
  })),
);

// Lazy load chart UI components
const LazyChartContainer = lazy(() =>
  import("@/components/ui/chart").then((module) => ({
    default: module.ChartContainer,
  })),
);
const LazyChartTooltip = lazy(() =>
  import("@/components/ui/chart").then((module) => ({
    default: module.ChartTooltip,
  })),
);
const LazyChartTooltipContent = lazy(() =>
  import("@/components/ui/chart").then((module) => ({
    default: module.ChartTooltipContent,
  })),
);

interface CashFlowData {
  date: string;
  income: number;
  expenses: number;
  balance: number;
}

interface ChartConfig {
  [key: string]: { label: string; color?: string };
  income: { label: string; color: string };
  expenses: { label: string; color: string };
  balance: { label: string; color: string };
}

// Memoized chart configuration to prevent recreation on every render
const chartConfig: ChartConfig = {
  income: {
    label: "Income",
    color: "var(--chart-1)",
  },
  expenses: {
    label: "Expenses",
    color: "var(--chart-2)",
  },
  balance: {
    label: "Net Balance",
    color: "var(--chart-3)",
  },
};

// Loading skeleton component
const ChartLoadingSkeleton = React.memo(() => (
  <div className="h-[300px] w-full animate-pulse bg-gray-200 rounded-md" />
));
ChartLoadingSkeleton.displayName = "ChartLoadingSkeleton";

// Chart fallback component
const ChartFallback = React.memo(() => (
  <div className="h-[300px] w-full flex items-center justify-center">
    <div className="text-sm text-muted-foreground">Loading chart...</div>
  </div>
));
ChartFallback.displayName = "ChartFallback";

// Memoized chart component for better performance
const MemoizedChart = React.memo<{
  data: CashFlowData[];
  config: ChartConfig;
  formatCurrency: (value: number) => string;
}>(({ data, config, formatCurrency }) => (
  <Suspense fallback={<ChartFallback />}>
    <LazyChartContainer config={config as any} className="min-h-[200px] w-full">
      <LazyResponsiveContainer width="100%" height={300}>
        <LazyAreaChart
          data={data}
          margin={{ top: 10, right: 10, left: 10, bottom: 0 }}
        >
          <defs>
            <linearGradient id="incomeGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="var(--chart-1)" stopOpacity={0.3} />
              <stop offset="95%" stopColor="var(--chart-1)" stopOpacity={0.1} />
            </linearGradient>
            <linearGradient id="expenseGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="var(--chart-2)" stopOpacity={0.3} />
              <stop offset="95%" stopColor="var(--chart-2)" stopOpacity={0.1} />
            </linearGradient>
          </defs>
          <LazyCartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <LazyXAxis
            dataKey="date"
            tickLine={false}
            axisLine={false}
            className="text-xs fill-muted-foreground"
            tickFormatter={(value) => {
              const date = new Date(value);
              return date.toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
              });
            }}
          />
          <LazyChartTooltip
            content={
              <LazyChartTooltipContent
                labelFormatter={(value) => {
                  const date = new Date(value as string);
                  return date.toLocaleDateString("en-US", {
                    weekday: "short",
                    month: "short",
                    day: "numeric",
                  });
                }}
                formatter={(value: any, name: any) => [
                  formatCurrency(Number(value)),
                  config[String(name)]?.label || String(name),
                ]}
              />
            }
          />
          {React.createElement(LazyArea as any, {
            type: "monotone",
            dataKey: "income",
            stackId: "1",
            stroke: "var(--chart-1)",
            fill: "url(#incomeGradient)",
            strokeWidth: 2,
          })}
          {React.createElement(LazyArea as any, {
            type: "monotone",
            dataKey: "expenses",
            stackId: "1",
            stroke: "var(--chart-2)",
            fill: "url(#expenseGradient)",
            strokeWidth: 2,
          })}
        </LazyAreaChart>
      </LazyResponsiveContainer>
    </LazyChartContainer>
  </Suspense>
));
MemoizedChart.displayName = "MemoizedChart";

// Time range options with memoization
const timeRangeOptions = [
  { value: "7d", label: "7 days", days: 7 },
  { value: "30d", label: "30 days", days: 30 },
  { value: "90d", label: "90 days", days: 90 },
  { value: "365d", label: "1 year", days: 365 },
] as const;

export const OptimizedCashFlowChart = React.memo(() => {
  const { user, profile } = useAuth();
  const isMobile = useIsMobile();
  const [timeRange, setTimeRange] = useState("90d");
  const [chartData, setChartData] = useState<CashFlowData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const currency = profile?.currency_code || "KWD";

  // Memoized currency formatter to prevent recreation
  const formatCurrency = useCallback(
    (value: number) => {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(value);
    },
    [currency],
  );

  // Optimized mobile detection effect
  useEffect(() => {
    if (isMobile && timeRange === "365d") {
      setTimeRange("30d"); // Reduce data load on mobile
    }
  }, [isMobile, timeRange]);

  // Memoized data loading function with proper cleanup
  const loadCashFlowData = useCallback(
    async (signal?: AbortSignal) => {
      if (!user) return;

      setLoading(true);
      setError(null);

      try {
        // TODO: Implement real data fetching from Supabase with AbortController
        // This would involve calling getUserTransactions and aggregating by date ranges
        // const transactions = await getUserTransactions(user.id, { signal })
        // const aggregatedData = aggregateTransactionsByTimeRange(transactions, timeRange)
        // setChartData(aggregatedData)

        // For now, simulate loading delay and show empty data until real implementation
        await new Promise((resolve) => setTimeout(resolve, 100));

        if (signal?.aborted) return;

        setChartData([]);
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          return; // Request was cancelled, don't update state
        }
        console.error("Failed to load cash flow data:", error);
        setError("Failed to load cash flow data");
        setChartData([]);
      } finally {
        if (!signal?.aborted) {
          setLoading(false);
        }
      }
    },
    [user],
  );

  // Effect with cleanup for data fetching
  useEffect(() => {
    const abortController = new AbortController();
    loadCashFlowData(abortController.signal);

    return () => {
      abortController.abort();
    };
  }, [loadCashFlowData]);

  // Memoized filtered data to prevent unnecessary recalculations
  const filteredData = useMemo(() => {
    if (!chartData.length) return [];

    const selectedRange = timeRangeOptions.find(
      (option) => option.value === timeRange,
    );
    if (!selectedRange) return chartData;

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - selectedRange.days);

    return chartData.filter((item) => new Date(item.date) >= cutoffDate);
  }, [chartData, timeRange]);

  // Memoized summary calculations
  const summary = useMemo(() => {
    if (!filteredData.length)
      return { totalIncome: 0, totalExpenses: 0, netChange: 0 };

    const totalIncome = filteredData.reduce(
      (sum, item) => sum + item.income,
      0,
    );
    const totalExpenses = filteredData.reduce(
      (sum, item) => sum + item.expenses,
      0,
    );
    const netChange = totalIncome - totalExpenses;

    return { totalIncome, totalExpenses, netChange };
  }, [filteredData]);

  // Memoized time range controls
  const timeRangeControls = useMemo(
    () =>
      isMobile ? (
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-auto">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {timeRangeOptions
              .filter((option) => !isMobile || option.value !== "365d") // Exclude year view on mobile
              .map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      ) : (
        <ToggleGroup
          type="single"
          value={timeRange}
          onValueChange={setTimeRange}
        >
          {timeRangeOptions.map((option) => (
            <ToggleGroupItem key={option.value} value={option.value} size="sm">
              {option.label}
            </ToggleGroupItem>
          ))}
        </ToggleGroup>
      ),
    [timeRange, isMobile],
  );

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cash Flow Trends</CardTitle>
          <CardDescription>Loading financial data...</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartLoadingSkeleton />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cash Flow Trends</CardTitle>
          <CardDescription className="text-red-600">{error}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full flex items-center justify-center">
            <button
              onClick={() => loadCashFlowData()}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!filteredData.length) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Cash Flow Trends</CardTitle>
            <CardDescription>No transaction data available</CardDescription>
          </div>
          <CardAction>{timeRangeControls}</CardAction>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full flex items-center justify-center border-2 border-dashed border-gray-300 rounded-md">
            <div className="text-center text-gray-500">
              <div className="text-sm font-medium">No cash flow data</div>
              <div className="text-xs">
                Add transactions to see your financial trends
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Cash Flow Trends</CardTitle>
          <CardDescription>
            Income vs Expenses over{" "}
            {timeRangeOptions
              .find((opt) => opt.value === timeRange)
              ?.label.toLowerCase()}
          </CardDescription>
        </div>
        <CardAction>{timeRangeControls}</CardAction>
      </CardHeader>
      <CardContent>
        <MemoizedChart
          data={filteredData}
          config={chartConfig}
          formatCurrency={formatCurrency}
        />
        {/* Summary statistics */}
        <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="font-medium text-green-600">
              {formatCurrency(summary.totalIncome)}
            </div>
            <div className="text-muted-foreground">Total Income</div>
          </div>
          <div className="text-center">
            <div className="font-medium text-red-600">
              {formatCurrency(summary.totalExpenses)}
            </div>
            <div className="text-muted-foreground">Total Expenses</div>
          </div>
          <div className="text-center">
            <div
              className={`font-medium ${summary.netChange >= 0 ? "text-green-600" : "text-red-600"}`}
            >
              {formatCurrency(summary.netChange)}
            </div>
            <div className="text-muted-foreground">Net Change</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

OptimizedCashFlowChart.displayName = "OptimizedCashFlowChart";
