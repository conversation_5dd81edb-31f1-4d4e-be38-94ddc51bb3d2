"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

function SkipLink({ href, children, className }: SkipLinkProps) {
  return (
    <a
      href={href}
      className={cn(
        "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:rounded-md focus:shadow-lg transition-all",
        className,
      )}
    >
      {children}
    </a>
  );
}

interface VisuallyHiddenProps {
  children: React.ReactNode;
  asChild?: boolean;
}

function VisuallyHidden({ children, asChild = false }: VisuallyHiddenProps) {
  const Comp = asChild ? React.Fragment : "span";

  return <Comp className="sr-only">{children}</Comp>;
}

interface FocusTrapProps {
  children: React.ReactNode;
  enabled?: boolean;
  className?: string;
}

function FocusTrap({ children, enabled = true, className }: FocusTrapProps) {
  const containerRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!enabled || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[
      focusableElements.length - 1
    ] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== "Tab") return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    };

    container.addEventListener("keydown", handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener("keydown", handleTabKey);
    };
  }, [enabled]);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
}

interface LiveRegionProps {
  children: React.ReactNode;
  politeness?: "polite" | "assertive" | "off";
  atomic?: boolean;
  className?: string;
}

function LiveRegion({
  children,
  politeness = "polite",
  atomic = false,
  className,
}: LiveRegionProps) {
  return (
    <div
      aria-live={politeness}
      aria-atomic={atomic}
      className={cn("sr-only", className)}
    >
      {children}
    </div>
  );
}

interface KeyboardShortcutProps {
  keys: string[];
  description: string;
  onActivate: () => void;
  disabled?: boolean;
}

function useKeyboardShortcut({
  keys,
  description,
  onActivate,
  disabled = false,
}: KeyboardShortcutProps) {
  React.useEffect(() => {
    if (disabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      const pressedKeys: string[] = [];

      if (event.ctrlKey) pressedKeys.push("ctrl");
      if (event.metaKey) pressedKeys.push("cmd");
      if (event.shiftKey) pressedKeys.push("shift");
      if (event.altKey) pressedKeys.push("alt");

      pressedKeys.push(event.key.toLowerCase());

      const shortcutMatch = keys.every((key) =>
        pressedKeys.includes(key.toLowerCase()),
      );

      if (shortcutMatch && pressedKeys.length === keys.length) {
        event.preventDefault();
        onActivate();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [keys, onActivate, disabled]);

  return { description };
}

interface TouchTargetProps {
  children: React.ReactNode;
  className?: string;
  minSize?: number;
}

function TouchTarget({ children, className, minSize = 44 }: TouchTargetProps) {
  return (
    <div
      className={cn(
        "inline-flex items-center justify-center touch-manipulation",
        className,
      )}
      style={{ minWidth: minSize, minHeight: minSize }}
    >
      {children}
    </div>
  );
}

interface ReducedMotionProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

function ReducedMotion({ children, fallback }: ReducedMotionProps) {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false);

  React.useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  return prefersReducedMotion && fallback ? fallback : children;
}

export {
  SkipLink,
  VisuallyHidden,
  FocusTrap,
  LiveRegion,
  useKeyboardShortcut,
  TouchTarget,
  ReducedMotion,
};
