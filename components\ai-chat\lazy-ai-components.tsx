"use client";

import { lazy, Suspense } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Bo<PERSON>, Upload, Lightbulb, Loader2 } from "lucide-react";
import { ErrorBoundary } from "@/components/error-boundary";

// Lazy load AI components to reduce initial bundle size - FIXED VERSION
const FinancialChatInterface = lazy(async () => {
  const module = await import("./financial-chat-interface");
  return { default: module.FinancialChatInterface };
});

const ReceiptProcessor = lazy(async () => {
  const module = await import("./receipt-processor");
  return { default: module.ReceiptProcessor };
});

// Loading components for each AI feature
function ChatLoadingSkeleton() {
  return (
    <Card className="h-[600px]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bot className="w-5 h-5" />
          <Skeleton className="h-6 w-48" />
        </CardTitle>
        <CardDescription>
          <Skeleton className="h-4 w-64" />
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Chat messages skeleton */}
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex gap-3">
              <Skeleton className="w-8 h-8 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
          ))}
        </div>

        {/* Input skeleton */}
        <div className="flex gap-2">
          <Skeleton className="flex-1 h-10" />
          <Skeleton className="w-10 h-10" />
        </div>

        {/* Suggested questions skeleton */}
        <div className="grid grid-cols-2 gap-2">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-8" />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function ReceiptLoadingSkeleton() {
  return (
    <Card className="h-[600px]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="w-5 h-5" />
          <Skeleton className="h-6 w-48" />
        </CardTitle>
        <CardDescription>
          <Skeleton className="h-4 w-64" />
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Upload area skeleton */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8">
          <div className="text-center space-y-4">
            <Skeleton className="w-16 h-16 mx-auto rounded" />
            <Skeleton className="h-6 w-48 mx-auto" />
            <Skeleton className="h-4 w-64 mx-auto" />
          </div>
        </div>

        {/* Processing steps skeleton */}
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center gap-3">
              <Skeleton className="w-6 h-6 rounded-full" />
              <Skeleton className="h-4 flex-1" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function InsightsLoadingSkeleton() {
  return (
    <Card className="h-[600px]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="w-5 h-5" />
          <Skeleton className="h-6 w-48" />
        </CardTitle>
        <CardDescription>
          <Skeleton className="h-4 w-64" />
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Insights cards skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardContent className="p-4 space-y-2">
                <Skeleton className="h-5 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Chart skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-48 w-full" />
        </div>
      </CardContent>
    </Card>
  );
}

// Loading fallback with progress indicator
function AILoadingFallback({ feature }: { feature: string }) {
  return (
    <div className="flex items-center justify-center h-[600px]">
      <div className="text-center space-y-4">
        <Loader2 className="w-8 h-8 animate-spin mx-auto text-blue-500" />
        <div className="space-y-2">
          <p className="text-lg font-medium">Loading {feature}...</p>
          <p className="text-sm text-gray-600">
            Initializing AI components for the best experience
          </p>
        </div>
      </div>
    </div>
  );
}

// Error fallback for AI components
function AIErrorFallback({
  feature,
  onRetry,
}: {
  feature: string;
  onRetry: () => void;
}) {
  return (
    <Card className="h-[600px] flex items-center justify-center">
      <CardContent className="text-center space-y-4">
        <div className="text-red-500">
          <Bot className="w-12 h-12 mx-auto mb-4" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Failed to load {feature}</h3>
          <p className="text-sm text-gray-600">
            There was an error loading the AI component. This might be due to
            network issues or the AI service being temporarily unavailable.
          </p>
        </div>
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      </CardContent>
    </Card>
  );
}

import { DbUser, UserProfile } from "@/lib/types/database";

// Lazy-loaded chat interface with error boundary
export function LazyFinancialChat({
  user,
  profile,
}: {
  user: DbUser | null;
  profile: UserProfile | null;
}) {
  return (
    <ErrorBoundary
      fallback={
        <AIErrorFallback
          feature="Financial Chat"
          onRetry={() => window.location.reload()}
        />
      }
    >
      <Suspense fallback={<ChatLoadingSkeleton />}>
        <FinancialChatInterface user={user} profile={profile} />
      </Suspense>
    </ErrorBoundary>
  );
}

// Lazy-loaded receipt processor with error boundary
export function LazyReceiptProcessor({
  user,
  profile,
}: {
  user: DbUser | null;
  profile: UserProfile | null;
}) {
  return (
    <ErrorBoundary
      fallback={
        <AIErrorFallback
          feature="Receipt Processor"
          onRetry={() => window.location.reload()}
        />
      }
    >
      <Suspense fallback={<ReceiptLoadingSkeleton />}>
        <ReceiptProcessor user={user} profile={profile} />
      </Suspense>
    </ErrorBoundary>
  );
}

// Placeholder for insights (future feature)
export function LazyInsights() {
  return (
    <ErrorBoundary
      fallback={
        <AIErrorFallback
          feature="Smart Insights"
          onRetry={() => window.location.reload()}
        />
      }
    >
      <Suspense fallback={<InsightsLoadingSkeleton />}>
        <Card className="h-[600px]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="w-5 h-5" />
              Smart Insights
            </CardTitle>
            <CardDescription>
              AI-powered financial insights and recommendations
            </CardDescription>
          </CardHeader>
          <CardContent className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <Lightbulb className="w-16 h-16 mx-auto text-gray-400" />
              <div className="space-y-2">
                <h3 className="text-lg font-medium">
                  Smart Insights Coming Soon
                </h3>
                <p className="text-sm text-gray-600 max-w-md">
                  We're working on AI-powered insights that will analyze your
                  spending patterns, suggest budget optimizations, and help you
                  achieve your financial goals faster.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </Suspense>
    </ErrorBoundary>
  );
}

// Preload AI components for better UX
export function preloadAIComponents() {
  // Preload the AI components when user is likely to use them
  if (typeof window !== "undefined") {
    // Use requestIdleCallback if available, otherwise setTimeout
    const preload = () => {
      import("./financial-chat-interface");
      import("./receipt-processor");
    };

    if ("requestIdleCallback" in window) {
      requestIdleCallback(preload);
    } else {
      setTimeout(preload, 1000);
    }
  }
}
