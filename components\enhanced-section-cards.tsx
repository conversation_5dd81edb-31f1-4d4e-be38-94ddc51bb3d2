"use client";

import * as React from "react";
import {
  IconTrendingDown,
  IconTrendingUp,
  IconMinus,
} from "@tabler/icons-react";
import { useDashboardData } from "@/lib/hooks/use-dashboard-data";
import { useAuth } from "@/lib/contexts/auth-context";
import { Badge } from "@/components/ui/badge";
import {
  EnhancedCard,
  EnhancedCardContent,
  EnhancedCardHeader,
  EnhancedCardTitle,
  EnhancedCardAction,
} from "@/components/ui/enhanced-card";
import {
  EnhancedSkeleton,
  SkeletonCard,
} from "@/components/ui/enhanced-skeleton";
import { ErrorState } from "@/components/ui/error-states";
import {
  StaggeredAnimation,
  FadeInView,
} from "@/components/ui/page-transitions";
import { HoverScale, Counter } from "@/components/ui/micro-interactions";
import { EnhancedTooltip } from "@/components/ui/enhanced-tooltip";
import { cn } from "@/lib/utils";

// Helper function to get currency symbol
function getCurrencySymbol(currencyCode: string): string {
  const symbols: Record<string, string> = {
    USD: "$",
    EUR: "€",
    GBP: "£",
    JPY: "¥",
    CAD: "C$",
    AUD: "A$",
    CHF: "CHF",
    CNY: "¥",
    INR: "₹",
    BRL: "R$",
    ZAR: "R",
    KRW: "₩",
    SGD: "S$",
    HKD: "HK$",
    NOK: "kr",
    SEK: "kr",
    DKK: "kr",
    PLN: "zł",
    CZK: "Kč",
    HUF: "Ft",
    RUB: "₽",
    TRY: "₺",
    MXN: "$",
    THB: "฿",
    MYR: "RM",
    IDR: "Rp",
    PHP: "₱",
    VND: "₫",
    EGP: "E£",
    SAR: "SR",
    AED: "د.إ",
    ILS: "₪",
    RON: "lei",
    BGN: "лв",
    HRK: "kn",
    RSD: "дин",
    UAH: "₴",
    KZT: "₸",
    UZS: "soʻm",
    AMD: "֏",
    GEL: "₾",
    AZN: "₼",
    BYN: "Br",
    MDL: "L",
    TJS: "ЅМ",
    KGS: "с",
    TMT: "T",
    MNT: "₮",
    NPR: "Rs",
    LKR: "Rs",
    PKR: "Rs",
    BDT: "৳",
    MMK: "K",
    LAK: "₭",
    KHR: "៛",
    TWD: "NT$",
    NZD: "NZ$",
    FJD: "FJ$",
    PGK: "K",
    WST: "T",
    TOP: "T$",
    VUV: "Vt",
    SBD: "SI$",
    NCX: "F",
    XPF: "F",
  };
  return symbols[currencyCode] || currencyCode;
}

function formatCurrency(amount: number, currencyCode: string): string {
  const symbol = getCurrencySymbol(currencyCode);
  const absAmount = Math.abs(amount);

  if (absAmount >= 1000000) {
    return `${symbol}${(amount / 1000000).toFixed(1)}M`;
  } else if (absAmount >= 1000) {
    return `${symbol}${(amount / 1000).toFixed(1)}K`;
  } else {
    return `${symbol}${amount.toFixed(2)}`;
  }
}

interface FinancialCardProps {
  title: string;
  amount: number;
  currencyCode: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  icon?: React.ReactNode;
  variant?: "default" | "success" | "warning" | "error";
  loading?: boolean;
  tooltip?: string;
  className?: string;
}

function FinancialCard({
  title,
  amount,
  currencyCode,
  trend,
  icon,
  variant = "default",
  loading = false,
  tooltip,
  className,
}: FinancialCardProps) {
  const cardContent = (
    <HoverScale>
      <EnhancedCard
        variant="interactive"
        className={cn(
          "transition-all duration-200",
          variant === "success" && "border-success/20 bg-success/5",
          variant === "warning" && "border-warning/20 bg-warning/5",
          variant === "error" && "border-destructive/20 bg-destructive/5",
          className,
        )}
        loading={loading}
      >
        <EnhancedCardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <EnhancedCardTitle className="text-sm font-medium text-muted-foreground">
              {title}
            </EnhancedCardTitle>
            {icon && <div className="text-muted-foreground">{icon}</div>}
          </div>
        </EnhancedCardHeader>

        <EnhancedCardContent>
          <div className="space-y-2">
            <div className="text-2xl font-bold">
              {loading ? (
                <EnhancedSkeleton className="h-8 w-24" />
              ) : (
                <Counter
                  from={0}
                  to={amount}
                  duration={1.5}
                  prefix={getCurrencySymbol(currencyCode)}
                />
              )}
            </div>

            {trend && !loading && (
              <div className="flex items-center space-x-1">
                {trend.isPositive ? (
                  <IconTrendingUp className="h-4 w-4 text-success" />
                ) : trend.value === 0 ? (
                  <IconMinus className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <IconTrendingDown className="h-4 w-4 text-destructive" />
                )}

                <Badge
                  variant={
                    trend.isPositive
                      ? "default"
                      : trend.value === 0
                        ? "secondary"
                        : "destructive"
                  }
                  className="text-xs"
                >
                  {trend.value > 0 && "+"}
                  {trend.value.toFixed(1)}%
                </Badge>

                <span className="text-xs text-muted-foreground">
                  vs {trend.period}
                </span>
              </div>
            )}

            {loading && <EnhancedSkeleton className="h-4 w-20" />}
          </div>
        </EnhancedCardContent>
      </EnhancedCard>
    </HoverScale>
  );

  if (tooltip) {
    return <EnhancedTooltip content={tooltip}>{cardContent}</EnhancedTooltip>;
  }

  return cardContent;
}

export function EnhancedSectionCards() {
  const { user, profile } = useAuth();
  const { data, loading, error, refetch } = useDashboardData();

  if (error) {
    return (
      <FadeInView>
        <ErrorState
          title="Failed to load financial summary"
          description="We couldn't load your financial data. Please try again."
          action={{
            label: "Retry",
            onClick: refetch,
          }}
        />
      </FadeInView>
    );
  }

  const currencyCode = profile?.currency_code || "USD";

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 px-4 lg:px-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <SkeletonCard key={i} />
        ))}
      </div>
    );
  }

  const cards = [
    {
      title: "Total Balance",
      amount: data?.balance || 0,
      trend: {
        value: 12.5,
        isPositive: true,
        period: "last month",
      },
      variant: "success" as const,
      tooltip: "Your current account balance including all income and expenses",
    },
    {
      title: "Monthly Income",
      amount: data?.income || 0,
      trend: {
        value: 8.2,
        isPositive: true,
        period: "last month",
      },
      variant: "default" as const,
      tooltip: "Total income received this month",
    },
    {
      title: "Monthly Expenses",
      amount: data?.expenses || 0,
      trend: {
        value: -3.1,
        isPositive: false,
        period: "last month",
      },
      variant: "warning" as const,
      tooltip: "Total expenses for this month",
    },
  ];

  return (
    <div className="px-4 lg:px-6">
      <StaggeredAnimation
        staggerDelay={0.1}
        className="grid gap-4 md:grid-cols-2 lg:grid-cols-3"
      >
        {cards.map((card, index) => (
          <FinancialCard
            key={card.title}
            title={card.title}
            amount={card.amount}
            currencyCode={currencyCode}
            trend={card.trend}
            variant={card.variant}
            tooltip={card.tooltip}
          />
        ))}
      </StaggeredAnimation>
    </div>
  );
}
