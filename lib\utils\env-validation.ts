/**
 * Environment variable validation utility
 * Ensures all required environment variables are present and valid
 */

import { log } from "@/lib/utils/logger";

interface EnvironmentConfig {
  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: string;
  NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY?: string;

  // Google AI Configuration
  GOOGLE_GENERATIVE_AI_API_KEY?: string;

  // Application Configuration
  NEXTAUTH_SECRET?: string;
  NEXTAUTH_URL?: string;

  // Development/Production flags
  NODE_ENV: "development" | "production" | "test";
}

class EnvironmentValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "EnvironmentValidationError";
  }
}

/**
 * Validates that all required environment variables are present
 * @throws {EnvironmentValidationError} If required variables are missing
 */
export function validateEnvironmentVariables(): EnvironmentConfig {
  const requiredVars = [
    "NEXT_PUBLIC_SUPABASE_URL",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY",
  ];

  const missingVars: string[] = [];
  const config: Partial<EnvironmentConfig> = {};

  // Check required variables
  for (const varName of requiredVars) {
    const value = process.env[varName];
    if (!value) {
      missingVars.push(varName);
    } else {
      (config as any)[varName] = value;
    }
  }

  // Check optional but important variables
  const optionalVars = [
    "SUPABASE_SERVICE_ROLE_KEY",
    "GOOGLE_GENERATIVE_AI_API_KEY",
    "NEXTAUTH_SECRET",
    "NEXTAUTH_URL",
  ];

  for (const varName of optionalVars) {
    const value = process.env[varName];
    if (value) {
      (config as any)[varName] = value;
    }
  }

  // Set NODE_ENV
  config.NODE_ENV =
    (process.env.NODE_ENV as "development" | "production" | "test") ||
    "development";

  if (missingVars.length > 0) {
    throw new EnvironmentValidationError(
      `Missing required environment variables: ${missingVars.join(", ")}\n` +
        "Please check your .env.local file and ensure all required variables are set.",
    );
  }

  // Validate URL formats
  if (
    config.NEXT_PUBLIC_SUPABASE_URL &&
    !isValidUrl(config.NEXT_PUBLIC_SUPABASE_URL)
  ) {
    throw new EnvironmentValidationError(
      "NEXT_PUBLIC_SUPABASE_URL must be a valid URL",
    );
  }

  if (config.NEXTAUTH_URL && !isValidUrl(config.NEXTAUTH_URL)) {
    throw new EnvironmentValidationError("NEXTAUTH_URL must be a valid URL");
  }

  return config as EnvironmentConfig;
}

/**
 * Validates URL format
 */
function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

/**
 * Gets a validated environment variable
 * @param key - Environment variable key
 * @param defaultValue - Default value if not found
 * @returns The environment variable value
 */
export function getEnvVar(
  key: keyof EnvironmentConfig,
  defaultValue?: string,
): string {
  // Handle client-side environment variables
  if (typeof window !== "undefined") {
    // On client side, only NEXT_PUBLIC_ variables are available
    if (key.startsWith("NEXT_PUBLIC_")) {
      const value = process.env[key];
      if (!value && !defaultValue) {
        throw new EnvironmentValidationError(
          `Environment variable ${key} is required but not set`,
        );
      }
      return value || defaultValue || "";
    } else {
      // Non-public variables are not available on client side
      if (!defaultValue) {
        log.warn(
          `Environment variable ${key} is not available on client side`,
          { key },
          "ENV",
        );
        return "";
      }
      return defaultValue;
    }
  }

  // Server-side handling
  const value = process.env[key];
  if (!value && !defaultValue) {
    throw new EnvironmentValidationError(
      `Environment variable ${key} is required but not set`,
    );
  }
  return value || defaultValue || "";
}

/**
 * Checks if we're in production environment
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === "production";
}

/**
 * Checks if we're in development environment
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === "development";
}

/**
 * Gets the application URL based on environment
 */
export function getAppUrl(): string {
  if (process.env.NEXTAUTH_URL) {
    return process.env.NEXTAUTH_URL;
  }

  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  return "http://localhost:3000";
}

// Note: Environment validation is now done explicitly where needed
// rather than on module load to avoid client-side issues
