import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import {
  RateLimiter,
  validateInput,
  ValidationSchemas,
} from "./input-validation";
import { z } from "zod";

/**
 * Comprehensive API security middleware
 * Implements OWASP security best practices for API endpoints
 */

// Security headers configuration
const SECURITY_HEADERS = {
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
  "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
} as const;

// Rate limiting configurations
const RATE_LIMITERS = {
  general: new RateLimiter(100, 15 * 60 * 1000), // 100 requests per 15 minutes
  auth: new RateLimiter(5, 15 * 60 * 1000), // 5 auth attempts per 15 minutes
  sensitive: new RateLimiter(10, 60 * 1000), // 10 sensitive ops per minute
  upload: new RateLimiter(5, 60 * 1000), // 5 uploads per minute
} as const;

// API endpoint patterns and their security requirements
const ENDPOINT_SECURITY = {
  "/api/auth/": {
    rateLimiter: "auth",
    requireAuth: false,
    validateOrigin: true,
  },
  "/api/financial-": {
    rateLimiter: "sensitive",
    requireAuth: true,
    validateOrigin: true,
  },
  "/api/process-receipt": {
    rateLimiter: "upload",
    requireAuth: true,
    validateOrigin: true,
  },
  "/api/admin/": {
    rateLimiter: "sensitive",
    requireAuth: true,
    requiredRole: "admin",
  },
} as const;

interface SecurityConfig {
  rateLimiter?: keyof typeof RATE_LIMITERS;
  requireAuth?: boolean;
  requiredRole?: "user" | "premium" | "admin";
  validateOrigin?: boolean;
  maxRequestSize?: number;
  allowedMethods?: string[];
  requireCSRF?: boolean;
}

interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    email: string;
    role: string;
  };
}

/**
 * Get client IP address for rate limiting
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get("x-forwarded-for");
  const realIP = request.headers.get("x-real-ip");
  const remoteAddr = request.headers.get("remote-addr");

  if (forwarded) {
    return forwarded.split(",")[0].trim();
  }

  return realIP || remoteAddr || "unknown";
}

/**
 * Validate request origin to prevent CSRF
 */
function validateOrigin(request: NextRequest): boolean {
  const origin = request.headers.get("origin");
  const referer = request.headers.get("referer");
  const host = request.headers.get("host");

  if (!origin && !referer) {
    // Allow requests without origin/referer for same-origin requests
    return true;
  }

  const allowedOrigins = [
    `https://${host}`,
    `http://${host}`, // Allow HTTP in development
    process.env.NEXT_PUBLIC_APP_URL,
    process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
  ].filter(Boolean);

  if (origin) {
    return allowedOrigins.includes(origin);
  }

  if (referer) {
    return allowedOrigins.some((allowed) => referer.startsWith(allowed || ""));
  }

  return false;
}

/**
 * Authenticate request using Supabase JWT
 */
async function authenticateRequest(
  request: NextRequest,
): Promise<AuthenticatedRequest["user"] | null> {
  const authHeader = request.headers.get("authorization");
  const token = authHeader?.replace("Bearer ", "");

  if (!token) {
    // Check for token in cookies as fallback
    const cookieToken = request.cookies.get("sb-access-token")?.value;
    if (!cookieToken) return null;
  }

  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
    );

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token);

    if (error || !user) {
      return null;
    }

    // Get user profile for role information
    const { data: profile } = await supabase
      .from("user_profiles")
      .select("role")
      .eq("user_id", user.id)
      .single();

    return {
      id: user.id,
      email: user.email!,
      role: profile?.role || "user",
    };
  } catch (error) {
    console.error("Authentication error:", error);
    return null;
  }
}

/**
 * Check if user has required role
 */
function hasRequiredRole(userRole: string, requiredRole: string): boolean {
  const roleHierarchy: Record<string, number> = {
    user: 1,
    premium: 2,
    admin: 3,
  };

  return (roleHierarchy[userRole] || 0) >= (roleHierarchy[requiredRole] || 0);
}

/**
 * Validate request size to prevent DoS
 */
function validateRequestSize(
  request: NextRequest,
  maxSize: number = 1024 * 1024,
): boolean {
  const contentLength = request.headers.get("content-length");
  if (contentLength && parseInt(contentLength) > maxSize) {
    return false;
  }
  return true;
}

/**
 * Sanitize request headers
 */
function sanitizeHeaders(request: NextRequest): Record<string, string> {
  const dangerousHeaders = [
    "x-forwarded-host",
    "x-forwarded-proto",
    "x-forwarded-server",
  ];

  const sanitized: Record<string, string> = {};

  request.headers.forEach((value, key) => {
    if (!dangerousHeaders.includes(key.toLowerCase())) {
      // Sanitize header values
      sanitized[key] = value.replace(/[^\x20-\x7E]/g, "").slice(0, 1000);
    }
  });

  return sanitized;
}

/**
 * Log security events
 */
function logSecurityEvent(event: {
  type:
    | "rate_limit"
    | "auth_failure"
    | "invalid_origin"
    | "oversized_request"
    | "unauthorized_access";
  ip: string;
  userAgent?: string;
  endpoint?: string;
  userId?: string;
  details?: Record<string, unknown>;
}) {
  const timestamp = new Date().toISOString();

  // In production, this should go to a proper logging service
  console.warn(`[SECURITY] ${timestamp}`, {
    ...event,
    severity: event.type === "rate_limit" ? "medium" : "high",
  });

  // TODO: Implement proper security event logging
  // - Send to security monitoring service
  // - Alert on suspicious patterns
  // - Store in security audit log
}

/**
 * Main security middleware factory
 */
export function createSecurityMiddleware(config: SecurityConfig = {}) {
  return async function securityMiddleware(
    request: NextRequest,
    handler: (
      req: AuthenticatedRequest,
    ) => Promise<NextResponse> | NextResponse,
  ): Promise<NextResponse> {
    const startTime = Date.now();
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get("user-agent") || "unknown";
    const pathname = request.nextUrl.pathname;

    try {
      // 1. Validate request size
      if (!validateRequestSize(request, config.maxRequestSize)) {
        logSecurityEvent({
          type: "oversized_request",
          ip: clientIP,
          userAgent,
          endpoint: pathname,
          details: { contentLength: request.headers.get("content-length") },
        });

        return NextResponse.json(
          { error: "Request too large" },
          { status: 413 },
        );
      }

      // 2. Validate allowed methods
      if (
        config.allowedMethods &&
        !config.allowedMethods.includes(request.method)
      ) {
        return NextResponse.json(
          { error: "Method not allowed" },
          { status: 405, headers: { Allow: config.allowedMethods.join(", ") } },
        );
      }

      // 3. Validate origin for CSRF protection
      if (config.validateOrigin && !validateOrigin(request)) {
        logSecurityEvent({
          type: "invalid_origin",
          ip: clientIP,
          userAgent,
          endpoint: pathname,
          details: {
            origin: request.headers.get("origin"),
            referer: request.headers.get("referer"),
          },
        });

        return NextResponse.json({ error: "Invalid origin" }, { status: 403 });
      }

      // 4. Rate limiting
      if (config.rateLimiter) {
        const rateLimiter = RATE_LIMITERS[config.rateLimiter];
        if (!rateLimiter.checkLimit(clientIP)) {
          logSecurityEvent({
            type: "rate_limit",
            ip: clientIP,
            userAgent,
            endpoint: pathname,
            details: { limiterType: config.rateLimiter },
          });

          return NextResponse.json(
            {
              error: "Rate limit exceeded",
              retryAfter: Math.ceil(15 * 60), // 15 minutes in seconds
            },
            {
              status: 429,
              headers: {
                "Retry-After": "900", // 15 minutes
                "X-RateLimit-Remaining": rateLimiter
                  .getRemainingAttempts(clientIP)
                  .toString(),
              },
            },
          );
        }
      }

      // 5. Authentication
      let user: AuthenticatedRequest["user"] = undefined;

      if (config.requireAuth) {
        user = (await authenticateRequest(request)) || undefined;

        if (!user) {
          logSecurityEvent({
            type: "auth_failure",
            ip: clientIP,
            userAgent,
            endpoint: pathname,
          });

          return NextResponse.json(
            { error: "Authentication required" },
            { status: 401 },
          );
        }

        // 6. Role-based authorization
        if (
          config.requiredRole &&
          !hasRequiredRole(user.role, config.requiredRole)
        ) {
          logSecurityEvent({
            type: "unauthorized_access",
            ip: clientIP,
            userAgent,
            endpoint: pathname,
            userId: user.id,
            details: { userRole: user.role, requiredRole: config.requiredRole },
          });

          return NextResponse.json(
            { error: "Insufficient permissions" },
            { status: 403 },
          );
        }
      }

      // 7. Sanitize headers
      const sanitizedHeaders = sanitizeHeaders(request);

      // 8. Create authenticated request object
      const authenticatedRequest = request as AuthenticatedRequest;
      authenticatedRequest.user = user;

      // 9. Call the actual handler
      const response = await handler(authenticatedRequest);

      // 10. Add security headers to response
      Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      // 11. Add performance timing header
      const duration = Date.now() - startTime;
      response.headers.set("X-Response-Time", `${duration}ms`);

      return response;
    } catch (error) {
      console.error("Security middleware error:", error);

      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 },
      );
    }
  };
}

/**
 * Pre-configured security middleware for common use cases
 */
export const authMiddleware = createSecurityMiddleware({
  rateLimiter: "auth",
  requireAuth: false,
  validateOrigin: true,
  maxRequestSize: 1024 * 1024, // 1MB
  allowedMethods: ["POST"],
});

export const apiMiddleware = createSecurityMiddleware({
  rateLimiter: "general",
  requireAuth: true,
  validateOrigin: true,
  maxRequestSize: 10 * 1024 * 1024, // 10MB
  allowedMethods: ["GET", "POST", "PUT", "DELETE"],
});

export const adminMiddleware = createSecurityMiddleware({
  rateLimiter: "sensitive",
  requireAuth: true,
  requiredRole: "admin",
  validateOrigin: true,
  maxRequestSize: 1024 * 1024, // 1MB
  allowedMethods: ["GET", "POST", "PUT", "DELETE"],
});

export const uploadMiddleware = createSecurityMiddleware({
  rateLimiter: "upload",
  requireAuth: true,
  validateOrigin: true,
  maxRequestSize: 50 * 1024 * 1024, // 50MB
  allowedMethods: ["POST"],
});

/**
 * Utility function to apply security middleware to API routes
 */
export function withSecurity(
  handler: (req: AuthenticatedRequest) => Promise<NextResponse> | NextResponse,
  config?: SecurityConfig,
) {
  const middleware = createSecurityMiddleware(config);

  return async function secureHandler(
    request: NextRequest,
  ): Promise<NextResponse> {
    return middleware(request, handler);
  };
}

/**
 * Input validation middleware
 */
export function withValidation<T>(
  schema: z.ZodSchema<T>,
  handler: (
    req: AuthenticatedRequest,
    data: T,
  ) => Promise<NextResponse> | NextResponse,
) {
  return async function validationHandler(
    request: AuthenticatedRequest,
  ): Promise<NextResponse> {
    try {
      const body = await request.json();
      const result = validateInput(schema, body);

      if (!result.success) {
        return NextResponse.json(
          { error: "Validation failed", details: result.error },
          { status: 400 },
        );
      }

      return handler(request, result.data!);
    } catch (error) {
      return NextResponse.json({ error: "Invalid JSON" }, { status: 400 });
    }
  };
}

// Export types for external use
export type { AuthenticatedRequest, SecurityConfig };
