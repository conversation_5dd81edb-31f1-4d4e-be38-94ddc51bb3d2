# Finance Tracker - Developer Guide

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Getting Started](#getting-started)
4. [Development Workflow](#development-workflow)
5. [Code Structure](#code-structure)
6. [Security](#security)
7. [Performance](#performance)
8. [Testing](#testing)
9. [Deployment](#deployment)
10. [API Reference](#api-reference)
11. [Troubleshooting](#troubleshooting)

## Overview

Finance Tracker is a comprehensive personal finance management application built with modern web technologies. It provides users with AI-powered insights, transaction tracking, goal management, and financial analytics.

### Technology Stack

- **Frontend**: Next.js 15.4.4 with App Router, React 19.1.0
- **Styling**: Tailwind CSS v4 with shadcn/ui components
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **AI Integration**: Google Gemini API
- **Charts**: Recharts 2.15.4
- **Testing**: Jest + React Testing Library
- **Type Safety**: TypeScript with strict mode

### Key Features

- 📊 **Dashboard Analytics**: Real-time financial overview with interactive charts
- 💰 **Transaction Management**: Advanced table with filtering, sorting, and categorization
- 🎯 **Goal Tracking**: Set and monitor financial goals with progress indicators
- 🤖 **AI Chat**: Natural language financial assistance and receipt processing
- 📱 **Responsive Design**: Mobile-first approach with adaptive layouts
- 🔒 **Security**: Comprehensive authentication and input validation
- ⚡ **Performance**: Optimized components with lazy loading and memoization

## Architecture

### Application Structure

```
├── app/                    # Next.js App Router pages
│   ├── (auth)/            # Authentication pages
│   ├── dashboard/         # Main dashboard
│   ├── transactions/      # Transaction management
│   ├── goals/            # Goal tracking
│   ├── api/              # API routes
│   └── globals.css       # Global styles
├── components/            # Reusable React components
│   ├── dashboard/        # Dashboard-specific components
│   ├── ui/              # Base UI components (shadcn/ui)
│   ├── auth/            # Authentication components
│   └── forms/           # Form components
├── lib/                  # Utility libraries
│   ├── contexts/        # React contexts
│   ├── hooks/           # Custom hooks
│   ├── security/        # Security utilities
│   ├── supabase/        # Database queries and client
│   ├── types/           # TypeScript type definitions
│   └── utils/           # Helper functions
├── docs/                # Documentation
├── __tests__/           # Test files
└── public/              # Static assets
```

### Data Flow

1. **Authentication**: Supabase Auth handles user registration, login, and session management
2. **Data Layer**: React contexts provide auth state and user profile data
3. **API Layer**: Next.js API routes handle server-side operations
4. **UI Layer**: React components consume data through custom hooks
5. **State Management**: Local component state with React hooks

### Database Schema

```sql
-- User Profiles
user_profiles (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  full_name TEXT,
  email TEXT,
  currency_code VARCHAR(3) DEFAULT 'USD',
  timezone TEXT,
  role VARCHAR(20) DEFAULT 'user',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)

-- Transactions
transactions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  amount DECIMAL(12,2) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  type VARCHAR(10) CHECK (type IN ('income', 'expense')),
  date DATE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)

-- Goals
goals (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  name VARCHAR(200) NOT NULL,
  description TEXT,
  target_amount DECIMAL(12,2) NOT NULL,
  current_amount DECIMAL(12,2) DEFAULT 0,
  target_date DATE,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)
```

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Supabase account and project
- Google AI API key (for Gemini integration)

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd finance-tracker
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env.local
   ```

   Fill in the required environment variables:

   ```env
   # Supabase
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

   # Google AI
   GOOGLE_AI_API_KEY=your_google_ai_api_key

   # App Configuration
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Set up the database**
   - Create tables using Supabase dashboard or migration files
   - Set up Row Level Security (RLS) policies
   - Configure authentication providers

5. **Run the development server**

   ```bash
   npm run dev
   ```

6. **Run tests**
   ```bash
   npm test
   ```

## Development Workflow

### Code Style and Standards

- **TypeScript**: Strict mode enabled with comprehensive type checking
- **ESLint**: Next.js recommended configuration with custom rules
- **Prettier**: Automatic code formatting
- **Husky**: Pre-commit hooks for code quality

### Git Workflow

1. Create feature branch from `main`
2. Make changes with descriptive commit messages
3. Run tests and ensure code quality
4. Create pull request with detailed description
5. Code review and approval
6. Merge to `main` and deploy

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

### Branch Naming

- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

## Code Structure

### Component Organization

```typescript
// components/dashboard/financial-summary-cards.tsx
'use client'

import { memo, useMemo, useCallback } from 'react'
import { useAuth } from '@/lib/contexts/auth-context'
import { Card, CardHeader, CardTitle } from '@/components/ui/card'

interface FinancialSummaryCardsProps {
  // Props interface
}

// Memoized component for performance
export const FinancialSummaryCards = memo<FinancialSummaryCardsProps>(({
  // Implementation
}) => {
  // Hooks
  const { user, profile } = useAuth()

  // Memoized calculations
  const calculations = useMemo(() => {
    // Expensive calculations
  }, [dependencies])

  // Event handlers
  const handleAction = useCallback(() => {
    // Handle user actions
  }, [dependencies])

  // Render
  return (
    <Card>
      {/* Component JSX */}
    </Card>
  )
})

FinancialSummaryCards.displayName = 'FinancialSummaryCards'
```

### Custom Hooks Pattern

```typescript
// lib/hooks/use-dashboard-data.ts
export function useDashboardData() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = useCallback(async () => {
    // Data fetching logic
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}
```

### Context Pattern

```typescript
// lib/contexts/auth-context.tsx
interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within AuthProvider");
  }
  return context;
}
```

## Security

### Authentication & Authorization

- **Supabase Auth**: JWT-based authentication with row-level security
- **Route Protection**: AuthGuard components protect sensitive routes
- **Role-Based Access**: User roles (user, premium, admin) with hierarchical permissions
- **API Security**: Comprehensive middleware for rate limiting and validation

### Input Validation

```typescript
// lib/security/input-validation.ts
import { z } from "zod";

export const TransactionSchema = z.object({
  amount: z.number().min(0.01).max(999999999.99),
  description: z.string().max(500),
  category: z.string().min(1).max(50),
  type: z.enum(["income", "expense"]),
  date: z.string().datetime(),
});

// Usage in API routes
export async function POST(request: NextRequest) {
  return withSecurity(
    withValidation(TransactionSchema, async (req, data) => {
      // Handle validated request
    }),
  )(request);
}
```

### Security Best Practices

1. **Input Sanitization**: All user inputs are sanitized using DOMPurify
2. **SQL Injection Prevention**: Parameterized queries via Supabase
3. **XSS Protection**: Content Security Policy headers
4. **CSRF Protection**: Origin validation for state-changing requests
5. **Rate Limiting**: Multiple rate limiters for different endpoints
6. **Security Headers**: Comprehensive security headers on all responses

## Performance

### Optimization Strategies

1. **Code Splitting**: Lazy loading of heavy components
2. **Memoization**: React.memo, useMemo, and useCallback optimization
3. **Image Optimization**: Next.js Image component with proper sizing
4. **Bundle Analysis**: Regular bundle size monitoring
5. **Database Optimization**: Efficient queries with proper indexing

### Chart Performance

```typescript
// Lazy loading heavy chart components
const LazyAreaChart = lazy(() =>
  import('recharts').then(module => ({ default: module.AreaChart }))
)

// Memoized chart component
const MemoizedChart = memo(({ data, config }) => (
  <Suspense fallback={<ChartSkeleton />}>
    <LazyAreaChart data={data} />
  </Suspense>
))
```

### Performance Monitoring

- **Core Web Vitals**: LCP, FID, CLS monitoring
- **Bundle Size**: Webpack bundle analyzer
- **Runtime Performance**: React DevTools Profiler
- **Database Performance**: Supabase query optimization

## Testing

### Testing Strategy

1. **Unit Tests**: Individual component and function testing
2. **Integration Tests**: Component interaction testing
3. **E2E Tests**: Full user workflow testing
4. **Visual Regression**: UI consistency testing

### Test Structure

```typescript
// __tests__/components/dashboard/financial-summary-cards.test.tsx
import { render, screen } from '@testing-library/react'
import { FinancialSummaryCards } from '@/components/dashboard/financial-summary-cards'
import { AuthProvider } from '@/lib/contexts/auth-context'

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <AuthProvider>
      {component}
    </AuthProvider>
  )
}

describe('FinancialSummaryCards', () => {
  it('renders financial data correctly', () => {
    renderWithProviders(<FinancialSummaryCards />)
    // Test assertions
  })
})
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- financial-summary-cards.test.tsx
```

## Deployment

### Build Configuration

```bash
# Build for production
npm run build

# Analyze bundle size
npm run analyze

# Check type errors
npm run typecheck

# Run linter
npm run lint
```

### Environment Setup

1. **Development**: Local development with hot reloading
2. **Staging**: Preview deployments for testing
3. **Production**: Optimized build with CDN and edge caching

### Deployment Checklist

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Security headers configured
- [ ] Analytics and monitoring set up
- [ ] Error tracking enabled
- [ ] Performance monitoring active

## API Reference

### Authentication Endpoints

```typescript
// POST /api/auth/signin
{
  email: string
  password: string
}

// POST /api/auth/signup
{
  email: string
  password: string
  full_name?: string
}

// POST /api/auth/signout
// No body required
```

### Transaction Endpoints

```typescript
// GET /api/transactions
// Query parameters: page?, limit?, category?, type?, date_from?, date_to?

// POST /api/transactions
{
  amount: number
  description?: string
  category: string
  type: 'income' | 'expense'
  date: string
}

// PUT /api/transactions/[id]
{
  amount?: number
  description?: string
  category?: string
  type?: 'income' | 'expense'
  date?: string
}

// DELETE /api/transactions/[id]
// No body required
```

### Goal Endpoints

```typescript
// GET /api/goals
// Query parameters: page?, limit?, status?

// POST /api/goals
{
  name: string
  description?: string
  target_amount: number
  target_date: string
  status?: 'active' | 'completed' | 'paused' | 'cancelled'
}

// PUT /api/goals/[id]
{
  name?: string
  description?: string
  target_amount?: number
  current_amount?: number
  target_date?: string
  status?: 'active' | 'completed' | 'paused' | 'cancelled'
}
```

## Troubleshooting

### Common Issues

1. **Build Errors**: Check TypeScript types and import paths
2. **Database Connection**: Verify Supabase credentials and RLS policies
3. **Authentication Issues**: Check JWT token expiration and refresh logic
4. **Performance Issues**: Use React DevTools Profiler to identify bottlenecks

### Debug Mode

```bash
# Enable debug logging
DEBUG=* npm run dev

# Database query debugging
SUPABASE_DEBUG=true npm run dev
```

### Error Monitoring

- **Client-side**: Error boundaries with logging
- **Server-side**: Comprehensive error handling in API routes
- **Database**: Query error logging and monitoring

### Development Tools

- **React DevTools**: Component inspection and profiling
- **Supabase Dashboard**: Database management and query optimization
- **Chrome DevTools**: Network monitoring and performance analysis
- **Lighthouse**: Performance and accessibility auditing

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Ensure code quality standards
5. Submit a pull request

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] Security considerations addressed
- [ ] Performance impact assessed
- [ ] Accessibility requirements met

For more detailed information, see the [Contributing Guide](CONTRIBUTING.md).
