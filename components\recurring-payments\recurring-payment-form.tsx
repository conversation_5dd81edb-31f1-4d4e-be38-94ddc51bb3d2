"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/auth-context";
import {
  createRecurringPayment,
  updateRecurringPayment,
  getUserCategories,
} from "@/lib/supabase/queries";
import { RecurringPayment, Category } from "@/lib/types/database";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";
import { CalendarIcon, DollarSign, Loader2, Repeat } from "lucide-react";
import { format, addDays, addWeeks, addMonths, addYears } from "date-fns";

interface RecurringPaymentFormProps {
  payment?: RecurringPayment;
  onSuccess: () => void;
  onCancel: () => void;
}

export function RecurringPaymentForm({
  payment,
  onSuccess,
  onCancel,
}: RecurringPaymentFormProps) {
  const { user, profile } = useAuth();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    name: payment?.name || "",
    amount: payment?.amount?.toString() || "",
    description: payment?.description || "",
    category_id: payment?.category_id || "",
    frequency:
      payment?.frequency ||
      ("monthly" as "daily" | "weekly" | "monthly" | "yearly"),
    start_date: payment?.start_date || format(new Date(), "yyyy-MM-dd"),
    is_active: payment?.is_active ?? true,
  });

  useEffect(() => {
    loadCategories();
  }, [user]);

  const loadCategories = async () => {
    if (!user) return;

    try {
      const userCategories = await getUserCategories(user.id);
      setCategories(userCategories);
    } catch (err) {
      console.error("Error loading categories:", err);
      setError("Failed to load categories");
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setError("");
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
    setError("");
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const calculateNextPaymentDate = (startDate: string, frequency: string) => {
    const start = new Date(startDate);

    switch (frequency) {
      case "daily":
        return format(addDays(start, 1), "yyyy-MM-dd");
      case "weekly":
        return format(addWeeks(start, 1), "yyyy-MM-dd");
      case "monthly":
        return format(addMonths(start, 1), "yyyy-MM-dd");
      case "yearly":
        return format(addYears(start, 1), "yyyy-MM-dd");
      default:
        return format(addMonths(start, 1), "yyyy-MM-dd");
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError("Please enter a payment name");
      return false;
    }

    if (
      !formData.amount ||
      isNaN(Number(formData.amount)) ||
      Number(formData.amount) <= 0
    ) {
      setError("Please enter a valid amount greater than 0");
      return false;
    }

    if (!formData.category_id) {
      setError("Please select a category");
      return false;
    }

    if (!formData.start_date) {
      setError("Please select a start date");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !user) return;

    setLoading(true);
    setError("");

    try {
      const nextPaymentDate = calculateNextPaymentDate(
        formData.start_date,
        formData.frequency,
      );

      const paymentData = {
        user_id: user.id,
        name: formData.name.trim(),
        amount: Number(formData.amount),
        description: formData.description.trim(),
        category_id: formData.category_id || null,
        frequency: formData.frequency,
        start_date: formData.start_date,
        end_date: null, // Add required end_date field
        next_payment_date: nextPaymentDate,
        is_active: formData.is_active,
        currency_code: profile?.currency_code || "USD",
      };

      if (payment) {
        await updateRecurringPayment(payment.id, paymentData);
      } else {
        await createRecurringPayment(paymentData);
      }

      onSuccess();
    } catch (err) {
      console.error("Error saving recurring payment:", err);

      // Provide specific error messages based on error type
      let errorMessage = "Failed to save recurring payment. Please try again.";

      if (err instanceof Error) {
        if (err.message.includes("network") || err.message.includes("fetch")) {
          errorMessage =
            "Network error: Unable to save recurring payment. Please check your internet connection and try again.";
        } else if (
          err.message.includes("duplicate") ||
          err.message.includes("unique")
        ) {
          errorMessage =
            "A recurring payment with this name already exists. Please choose a different name.";
        } else if (
          err.message.includes("permission") ||
          err.message.includes("unauthorized")
        ) {
          errorMessage =
            "Permission denied: You may need to sign in again to save recurring payments.";
        } else if (
          err.message.includes("validation") ||
          err.message.includes("invalid")
        ) {
          errorMessage =
            "Invalid payment data: Please check that the amount is positive and the start date is valid.";
        } else if (err.message.includes("timeout")) {
          errorMessage =
            "Request timeout: The save operation took too long. Please try again.";
        } else if (err.message.trim()) {
          errorMessage = `Recurring payment save failed: ${err.message}`;
        }
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const expenseCategories = categories.filter(
    (cat) =>
      !["Salary", "Freelance", "Investment", "Other Income"].includes(cat.name),
  );

  const frequencyOptions = [
    { value: "daily", label: "Daily" },
    { value: "weekly", label: "Weekly" },
    { value: "monthly", label: "Monthly" },
    { value: "yearly", label: "Yearly" },
  ];

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Repeat className="w-5 h-5" />
          {payment ? "Edit Recurring Payment" : "Add New Recurring Payment"}
        </CardTitle>
        <CardDescription>
          {payment
            ? "Update recurring payment details"
            : "Set up a new recurring payment or subscription"}
        </CardDescription>
      </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Payment Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Payment Name *</Label>
            <Input
              id="name"
              name="name"
              placeholder="e.g., Netflix Subscription, Rent, Insurance"
              value={formData.name}
              onChange={handleInputChange}
            />
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">
              Amount ({profile?.currency_code || "USD"}) *
            </Label>
            <Input
              id="amount"
              name="amount"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={formData.amount}
              onChange={handleInputChange}
              className="text-lg"
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Optional description or notes"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
            />
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category_id">Category *</Label>
            <Select
              value={formData.category_id}
              onValueChange={(value) =>
                handleSelectChange("category_id", value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {expenseCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color || '#6B7280' }}
                      />
                      <span>{category.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Frequency */}
          <div className="space-y-2">
            <Label htmlFor="frequency">Payment Frequency *</Label>
            <Select
              value={formData.frequency}
              onValueChange={(value) => handleSelectChange("frequency", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                {frequencyOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label htmlFor="start_date">Start Date *</Label>
            <Input
              id="start_date"
              name="start_date"
              type="date"
              value={formData.start_date}
              onChange={handleInputChange}
            />
          </div>

          {/* Next Payment Preview */}
          {formData.start_date && formData.frequency && (
            <div className="p-4 bg-muted rounded-lg">
              <div className="text-sm text-muted-foreground">
                Next Payment Date:
              </div>
              <div className="font-medium">
                {format(
                  new Date(
                    calculateNextPaymentDate(
                      formData.start_date,
                      formData.frequency,
                    ),
                  ),
                  "MMM dd, yyyy",
                )}
              </div>
            </div>
          )}

          {/* Active Status */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) =>
                handleSwitchChange("is_active", checked)
              }
            />
            <Label htmlFor="is_active">
              Active Payment
              <span className="block text-sm text-muted-foreground">
                {formData.is_active
                  ? "Payment is active and will be processed"
                  : "Payment is paused"}
              </span>
            </Label>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {payment ? "Update Payment" : "Add Payment"}
            </Button>
          </div>
        </CardContent>
      </form>
    </Card>
  );
}
