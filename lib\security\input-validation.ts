import { z } from "zod";
import DOMPurify from "isomorphic-dompurify";

/**
 * Comprehensive input validation and sanitization utilities
 * Implements OWASP guidelines for input validation
 */

// Common validation schemas
export const ValidationSchemas = {
  // User input schemas
  email: z.string().email("Invalid email format").max(254, "Email too long"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(128, "Password too long")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      "Password must contain uppercase, lowercase, number, and special character",
    ),

  // Financial data schemas
  amount: z
    .number()
    .min(0.01, "Amount must be positive")
    .max(999999999.99, "Amount too large")
    .multipleOf(0.01, "Amount must have at most 2 decimal places"),

  currency: z
    .string()
    .length(3, "Currency code must be 3 characters")
    .regex(/^[A-Z]{3}$/, "Currency code must be uppercase letters"),

  // Text input schemas
  name: z
    .string()
    .min(1, "Name is required")
    .max(100, "Name too long")
    .regex(/^[a-zA-Z\s\-'\.]+$/, "Name contains invalid characters"),

  description: z.string().max(500, "Description too long").optional(),

  category: z
    .string()
    .min(1, "Category is required")
    .max(50, "Category name too long")
    .regex(/^[a-zA-Z0-9\s\-_&]+$/, "Category contains invalid characters"),

  // Date schemas
  date: z.string().datetime("Invalid date format").or(z.date()),

  // ID schemas
  uuid: z.string().uuid("Invalid UUID format"),

  // Pagination schemas
  page: z
    .number()
    .int("Page must be an integer")
    .min(1, "Page must be at least 1")
    .max(10000, "Page number too large"),

  limit: z
    .number()
    .int("Limit must be an integer")
    .min(1, "Limit must be at least 1")
    .max(100, "Limit too large"),

  // Search schemas
  searchQuery: z
    .string()
    .max(200, "Search query too long")
    .regex(
      /^[a-zA-Z0-9\s\-_.,!?]*$/,
      "Search query contains invalid characters",
    ),
};

// Transaction validation schema
export const TransactionSchema = z.object({
  id: ValidationSchemas.uuid.optional(),
  user_id: ValidationSchemas.uuid,
  amount: ValidationSchemas.amount,
  description: ValidationSchemas.description,
  category: ValidationSchemas.category,
  type: z.enum(["income", "expense"], {
    errorMap: () => ({ message: "Type must be income or expense" }),
  }),
  date: ValidationSchemas.date,
  created_at: ValidationSchemas.date.optional(),
  updated_at: ValidationSchemas.date.optional(),
});

// Goal validation schema
export const GoalSchema = z.object({
  id: ValidationSchemas.uuid.optional(),
  user_id: ValidationSchemas.uuid,
  name: ValidationSchemas.name,
  description: ValidationSchemas.description,
  target_amount: ValidationSchemas.amount,
  current_amount: ValidationSchemas.amount.default(0),
  target_date: ValidationSchemas.date,
  status: z.enum(["active", "completed", "paused", "cancelled"], {
    errorMap: () => ({ message: "Invalid goal status" }),
  }),
  created_at: ValidationSchemas.date.optional(),
  updated_at: ValidationSchemas.date.optional(),
});

// User profile validation schema
export const UserProfileSchema = z.object({
  id: ValidationSchemas.uuid.optional(),
  user_id: ValidationSchemas.uuid,
  full_name: ValidationSchemas.name.optional(),
  email: ValidationSchemas.email,
  currency_code: ValidationSchemas.currency.default("USD"),
  timezone: z.string().max(50).optional(),
  created_at: ValidationSchemas.date.optional(),
  updated_at: ValidationSchemas.date.optional(),
});

/**
 * Sanitizes HTML content to prevent XSS attacks
 */
export function sanitizeHtml(input: string): string {
  if (typeof input !== "string") {
    throw new Error("Input must be a string");
  }

  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [], // No HTML tags allowed
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true, // Keep text content
  });
}

/**
 * Sanitizes plain text input
 */
export function sanitizeText(input: string): string {
  if (typeof input !== "string") {
    throw new Error("Input must be a string");
  }

  return input
    .trim()
    .replace(/\s+/g, " ") // Normalize whitespace
    .slice(0, 1000); // Limit length
}

/**
 * Validates and sanitizes search queries
 */
export function validateSearchQuery(query: string): string {
  const sanitized = sanitizeText(query);
  const result = ValidationSchemas.searchQuery.safeParse(sanitized);

  if (!result.success) {
    throw new Error(`Invalid search query: ${result.error.issues[0].message}`);
  }

  return result.data;
}

/**
 * Validates pagination parameters
 */
export function validatePagination(page?: number, limit?: number) {
  const validatedPage = ValidationSchemas.page.parse(page || 1);
  const validatedLimit = ValidationSchemas.limit.parse(limit || 20);

  return { page: validatedPage, limit: validatedLimit };
}

/**
 * Validates file uploads
 */
export function validateFileUpload(
  file: File,
  options: {
    maxSize?: number;
    allowedTypes?: string[];
    allowedExtensions?: string[];
  } = {},
) {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB default
    allowedTypes = ["image/jpeg", "image/png", "image/webp", "application/pdf"],
    allowedExtensions = [".jpg", ".jpeg", ".png", ".webp", ".pdf"],
  } = options;

  // Check file size
  if (file.size > maxSize) {
    throw new Error(
      `File size exceeds ${Math.round(maxSize / 1024 / 1024)}MB limit`,
    );
  }

  // Check MIME type
  if (!allowedTypes.includes(file.type)) {
    throw new Error(`File type ${file.type} not allowed`);
  }

  // Check file extension
  const extension = "." + file.name.split(".").pop()?.toLowerCase();
  if (!allowedExtensions.includes(extension)) {
    throw new Error(`File extension ${extension} not allowed`);
  }

  // Additional security checks
  if (file.name.length > 255) {
    throw new Error("Filename too long");
  }

  if (!/^[a-zA-Z0-9\-_. ]+$/.test(file.name)) {
    throw new Error("Filename contains invalid characters");
  }

  return true;
}

/**
 * Rate limiting validation
 */
export class RateLimiter {
  private attempts: Map<string, { count: number; resetTime: number }> =
    new Map();

  constructor(
    private maxAttempts: number = 10,
    private windowMs: number = 15 * 60 * 1000, // 15 minutes
  ) {}

  /**
   * Check if request is within rate limits
   */
  checkLimit(identifier: string): boolean {
    const now = Date.now();
    const record = this.attempts.get(identifier);

    if (!record || now > record.resetTime) {
      // Reset or create new record
      this.attempts.set(identifier, {
        count: 1,
        resetTime: now + this.windowMs,
      });
      return true;
    }

    if (record.count >= this.maxAttempts) {
      return false;
    }

    record.count++;
    return true;
  }

  /**
   * Get remaining attempts
   */
  getRemainingAttempts(identifier: string): number {
    const record = this.attempts.get(identifier);
    if (!record || Date.now() > record.resetTime) {
      return this.maxAttempts;
    }
    return Math.max(0, this.maxAttempts - record.count);
  }

  /**
   * Clear attempts for identifier
   */
  clear(identifier: string): void {
    this.attempts.delete(identifier);
  }
}

/**
 * SQL injection prevention utilities
 */
export function escapeSqlIdentifier(identifier: string): string {
  // Only allow alphanumeric characters, underscores, and hyphens
  if (!/^[a-zA-Z0-9_-]+$/.test(identifier)) {
    throw new Error("Invalid SQL identifier");
  }
  return identifier;
}

/**
 * Validate API endpoint access
 */
export function validateApiAccess(
  userRole: string,
  requiredRole: string,
  resourceOwnerId?: string,
  userId?: string,
): boolean {
  // Admin can access everything
  if (userRole === "admin") return true;

  // Check role hierarchy
  const roleHierarchy: Record<string, number> = {
    user: 1,
    premium: 2,
    admin: 3,
  };

  const userLevel = roleHierarchy[userRole] || 0;
  const requiredLevel = roleHierarchy[requiredRole] || 0;

  if (userLevel < requiredLevel) return false;

  // Check resource ownership
  if (resourceOwnerId && userId && resourceOwnerId !== userId) {
    return false;
  }

  return true;
}

// Export types for external use
export type ValidationResult<T> = {
  success: boolean;
  data?: T;
  error?: string;
};

/**
 * Generic validation wrapper
 */
export function validateInput<T>(
  schema: z.ZodSchema<T>,
  input: unknown,
): ValidationResult<T> {
  try {
    const result = schema.parse(input);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.issues[0].message,
      };
    }
    return {
      success: false,
      error: "Validation failed",
    };
  }
}
