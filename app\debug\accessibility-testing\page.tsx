"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  StatusIndicator,
  GoalStatusIndicator,
  TransactionTypeIndicator,
  PriorityIndicator,
  ProgressIndicator,
} from "@/components/ui/status-indicator";
import {
  AccessibleInput,
  AccessibleTextarea,
  AccessibleSelect,
  AccessibleCheckbox,
} from "@/components/ui/accessible-form";
import { SelectItem } from "@/components/ui/select";
import {
  Eye,
  Keyboard,
  Volume2,
  MousePointer,
  CheckCircle,
  AlertTriangle,
  Info,
} from "lucide-react";

export default function AccessibilityTestingPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: "",
    category: "",
    newsletter: false,
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults((prev) => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${result}`,
    ]);
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Please enter a valid email address";
    }

    if (!formData.message.trim()) {
      errors.message = "Message is required";
    }

    if (!formData.category) {
      errors.category = "Please select a category";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      addTestResult("Form submitted successfully");
      alert("Form submitted successfully! (This is just a test)");
    } else {
      addTestResult("Form validation failed");
    }
  };

  const testKeyboardNavigation = () => {
    addTestResult("Keyboard navigation test started - use Tab to navigate");
    // Focus the first interactive element
    const firstButton = document.querySelector("button");
    firstButton?.focus();
  };

  const testScreenReaderContent = () => {
    addTestResult("Screen reader test - check ARIA labels and descriptions");
    // Announce to screen readers
    const announcement = document.createElement("div");
    announcement.setAttribute("aria-live", "polite");
    announcement.setAttribute("aria-atomic", "true");
    announcement.className = "sr-only";
    announcement.textContent =
      "Accessibility test started. Please navigate through the page to test screen reader compatibility.";
    document.body.appendChild(announcement);

    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  };

  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">Accessibility Testing</h1>
        <p className="text-gray-600">
          Test accessibility features including ARIA labels, keyboard
          navigation, and screen reader support.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Indicators Testing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              Status Indicators
            </CardTitle>
            <CardDescription>
              Test accessible status indicators with proper ARIA labels
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-medium mb-2">Basic Status Indicators:</h4>
                <div className="flex flex-wrap gap-2">
                  <StatusIndicator status="active" />
                  <StatusIndicator status="completed" />
                  <StatusIndicator status="paused" />
                  <StatusIndicator status="cancelled" />
                  <StatusIndicator status="pending" />
                  <StatusIndicator status="error" />
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Goal Status Indicators:</h4>
                <div className="space-y-2">
                  <GoalStatusIndicator status="active" progress={65} />
                  <GoalStatusIndicator status="completed" progress={100} />
                  <GoalStatusIndicator status="paused" progress={30} />
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">
                  Transaction Type Indicators:
                </h4>
                <div className="space-y-2">
                  <TransactionTypeIndicator type="income" amount={1500} />
                  <TransactionTypeIndicator type="expense" amount={250} />
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Priority Indicators:</h4>
                <div className="flex flex-wrap gap-2">
                  <PriorityIndicator priority="low" />
                  <PriorityIndicator priority="medium" />
                  <PriorityIndicator priority="high" />
                  <PriorityIndicator priority="urgent" />
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Progress Indicators:</h4>
                <div className="space-y-3">
                  <ProgressIndicator value={25} label="Emergency Fund" />
                  <ProgressIndicator value={75} label="Vacation Savings" />
                  <ProgressIndicator value={100} label="Debt Payoff" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Accessible Form Testing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Keyboard className="w-5 h-5" />
              Accessible Forms
            </CardTitle>
            <CardDescription>
              Test form accessibility with proper labels, hints, and error
              messages
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <AccessibleInput
                id="name"
                label="Full Name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                required
                error={formErrors.name}
                hint="Enter your first and last name"
                placeholder="John Doe"
              />

              <AccessibleInput
                id="email"
                label="Email Address"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                required
                error={formErrors.email}
                hint="We'll use this to send you updates"
                placeholder="<EMAIL>"
              />

              <AccessibleSelect
                id="category"
                label="Category"
                value={formData.category}
                onValueChange={(value) => handleInputChange("category", value)}
                required
                error={formErrors.category}
                hint="Choose the most relevant category"
              >
                <SelectItem value="general">General Inquiry</SelectItem>
                <SelectItem value="support">Technical Support</SelectItem>
                <SelectItem value="billing">Billing Question</SelectItem>
                <SelectItem value="feature">Feature Request</SelectItem>
              </AccessibleSelect>

              <AccessibleTextarea
                id="message"
                label="Message"
                value={formData.message}
                onChange={(e) => handleInputChange("message", e.target.value)}
                required
                error={formErrors.message}
                hint="Please provide as much detail as possible"
                placeholder="Tell us how we can help..."
                rows={4}
              />

              <AccessibleCheckbox
                id="newsletter"
                label="Subscribe to newsletter"
                checked={formData.newsletter}
                onCheckedChange={(checked) =>
                  handleInputChange("newsletter", checked)
                }
                hint="Receive updates about new features and tips"
              />

              <Button type="submit" className="w-full">
                Submit Form
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Testing Controls */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Accessibility Testing Controls</CardTitle>
            <CardDescription>
              Use these tools to test different aspects of accessibility
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                onClick={testKeyboardNavigation}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Keyboard className="w-4 h-4" />
                Test Keyboard Navigation
              </Button>

              <Button
                onClick={testScreenReaderContent}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Volume2 className="w-4 h-4" />
                Test Screen Reader
              </Button>

              <Button
                onClick={() =>
                  addTestResult(
                    "Focus indicators test - use Tab to see focus rings",
                  )
                }
                variant="outline"
                className="flex items-center gap-2"
              >
                <MousePointer className="w-4 h-4" />
                Test Focus Indicators
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Testing Instructions */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Alert>
                <Info className="w-4 h-4" />
                <AlertDescription>
                  <strong>Screen Reader Testing:</strong> Use NVDA (Windows),
                  JAWS (Windows), or VoiceOver (Mac) to test how content is
                  announced to screen readers.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <Keyboard className="w-4 h-4" />
                    Keyboard Navigation Tests:
                  </h4>
                  <ul className="list-disc list-inside text-sm space-y-1 text-gray-600">
                    <li>
                      Use Tab to navigate forward through interactive elements
                    </li>
                    <li>Use Shift+Tab to navigate backward</li>
                    <li>Use Enter/Space to activate buttons and checkboxes</li>
                    <li>Use arrow keys in select dropdowns</li>
                    <li>Verify focus indicators are visible and clear</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <Volume2 className="w-4 h-4" />
                    Screen Reader Tests:
                  </h4>
                  <ul className="list-disc list-inside text-sm space-y-1 text-gray-600">
                    <li>Verify status indicators announce their meaning</li>
                    <li>Check that form labels are properly associated</li>
                    <li>Ensure error messages are announced</li>
                    <li>Test that hints provide helpful context</li>
                    <li>Verify progress indicators announce percentages</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <Eye className="w-4 h-4" />
                    Visual Tests:
                  </h4>
                  <ul className="list-disc list-inside text-sm space-y-1 text-gray-600">
                    <li>Check color contrast meets WCAG standards</li>
                    <li>Verify information isn't conveyed by color alone</li>
                    <li>Test with browser zoom up to 200%</li>
                    <li>Ensure text remains readable at all zoom levels</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    Expected Results:
                  </h4>
                  <ul className="list-disc list-inside text-sm space-y-1 text-gray-600">
                    <li>All interactive elements are keyboard accessible</li>
                    <li>Status indicators include text descriptions</li>
                    <li>Form errors are clearly announced</li>
                    <li>Progress bars announce completion percentages</li>
                    <li>Focus indicators are clearly visible</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Test Results Log</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <h4 className="font-medium">Testing Actions:</h4>
                <Button
                  onClick={() => setTestResults([])}
                  variant="outline"
                  size="sm"
                >
                  Clear Log
                </Button>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg max-h-40 overflow-y-auto">
                {testResults.length === 0 ? (
                  <p className="text-gray-500 text-sm">
                    No test actions yet. Start testing above!
                  </p>
                ) : (
                  <div className="space-y-1">
                    {testResults.map((result, index) => (
                      <div key={index} className="text-sm font-mono">
                        {result}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
