"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";

const cardVariants = cva(
  "bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm transition-all duration-200 ease-out",
  {
    variants: {
      variant: {
        default: "hover:shadow-md hover:scale-[1.01]",
        interactive: "hover:shadow-lg hover:scale-[1.02] cursor-pointer",
        elevated: "shadow-lg hover:shadow-xl hover:scale-[1.01]",
        flat: "shadow-none border-0 bg-transparent",
      },
      padding: {
        default: "py-6",
        sm: "py-4",
        lg: "py-8",
        none: "py-0",
      },
    },
    defaultVariants: {
      variant: "default",
      padding: "default",
    },
  },
);

interface EnhancedCardProps
  extends React.ComponentProps<"div">,
    VariantProps<typeof cardVariants> {
  loading?: boolean;
  error?: boolean;
  success?: boolean;
}

function EnhancedCard({
  className,
  variant,
  padding,
  loading,
  error,
  success,
  children,
  ...props
}: EnhancedCardProps) {
  const cardRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (success && cardRef.current) {
      // Add success animation
      cardRef.current.style.animation = "pulse 0.5s ease-in-out";
      setTimeout(() => {
        if (cardRef.current) {
          cardRef.current.style.animation = "";
        }
      }, 500);
    }
  }, [success]);

  return (
    <div
      ref={cardRef}
      data-slot="enhanced-card"
      className={cn(
        cardVariants({ variant, padding }),
        loading && "opacity-60 pointer-events-none",
        error && "border-destructive/50 bg-destructive/5",
        success && "border-success/50 bg-success/5",
        className,
      )}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 rounded-xl">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
            <span className="text-sm text-muted-foreground">Loading...</span>
          </div>
        </div>
      )}
      {children}
    </div>
  );
}

function EnhancedCardHeader({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="enhanced-card-header"
      className={cn(
        "@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",
        className,
      )}
      {...props}
    />
  );
}

function EnhancedCardTitle({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="enhanced-card-title"
      className={cn("leading-none font-semibold", className)}
      {...props}
    />
  );
}

function EnhancedCardDescription({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="enhanced-card-description"
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  );
}

function EnhancedCardContent({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="enhanced-card-content"
      className={cn("px-6", className)}
      {...props}
    />
  );
}

function EnhancedCardFooter({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="enhanced-card-footer"
      className={cn("flex items-center px-6 [.border-t]:pt-6", className)}
      {...props}
    />
  );
}

function EnhancedCardAction({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="enhanced-card-action"
      className={cn(
        "col-start-2 row-span-2 row-start-1 self-start justify-self-end",
        className,
      )}
      {...props}
    />
  );
}

export {
  EnhancedCard,
  EnhancedCardHeader,
  EnhancedCardFooter,
  EnhancedCardTitle,
  EnhancedCardAction,
  EnhancedCardDescription,
  EnhancedCardContent,
  cardVariants,
};
