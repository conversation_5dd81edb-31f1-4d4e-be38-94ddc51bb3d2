"use client";

import { useRef, useEffect } from "react";
import { useChat } from "@ai-sdk/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Bot, User, Send, Loader2 } from "lucide-react";
import ReactMarkdown from "react-markdown";
import { DbUser, UserProfile } from "@/lib/types/database";

interface FinancialChatInterfaceProps {
  user: DbUser | null;
  profile: UserProfile | null;
}

// Removed suggested questions for cleaner UI

export function FinancialChatInterface({
  user,
  profile,
}: FinancialChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Create financial context from user data
  const financialContext = `
User Profile:
- Name: ${profile?.full_name || "User"}
- Currency: ${profile?.currency_code || "USD"}
- Monthly Income: ${profile?.monthly_income ? `${profile.currency_code || "USD"} ${profile.monthly_income}` : "Not specified"}
- Salary Payment Date: ${profile?.salary_payment_date ? `${profile.salary_payment_date} of each month` : "Not specified"}

You are a personal financial advisor AI assistant. Provide helpful, personalized financial advice based on the user's profile and questions. Be encouraging, practical, and specific in your recommendations.
  `.trim();

  const { messages, input, handleInputChange, handleSubmit, isLoading, error } =
    useChat({
      api: "/api/financial-chat",
      body: {
        financialContext,
        userId: user?.id,
      },
      initialMessages: [
        {
          id: "welcome",
          role: "assistant",
          content: `Hello ${profile?.full_name || "there"}! I'm your AI financial advisor. How can I help you with your finances today?`,
        },
      ],
    });

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;
    handleSubmit(e);
  };

  return (
    <div className="flex flex-col h-[calc(100vh-12rem)] bg-white dark:bg-gray-950 border border-gray-200 dark:border-gray-800 rounded-lg shadow-sm">
      {/* Messages Area */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-6 space-y-6">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
              >
                <div
                  className={`flex items-start gap-3 max-w-[75%] ${
                    message.role === "user" ? "flex-row-reverse" : "flex-row"
                  }`}
                >
                  <div
                    className={`p-2 rounded-full flex-shrink-0 ${
                      message.role === "user"
                        ? "bg-blue-600 text-white"
                        : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    {message.role === "user" ? (
                      <User className="w-4 h-4" />
                    ) : (
                      <Bot className="w-4 h-4" />
                    )}
                  </div>
                  <div
                    className={`px-4 py-3 rounded-lg ${
                      message.role === "user"
                        ? "bg-blue-600 text-white"
                        : "bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100"
                    }`}
                  >
                    <div
                      className={`prose prose-sm max-w-none ${
                        message.role === "user"
                          ? "prose-invert"
                          : "dark:prose-invert"
                      }`}
                    >
                      <ReactMarkdown>{message.content}</ReactMarkdown>
                    </div>
                    <div
                      className={`text-xs mt-2 ${
                        message.role === "user"
                          ? "text-blue-100"
                          : "text-gray-500 dark:text-gray-400"
                      }`}
                    >
                      {new Date().toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className="flex items-start gap-3 max-w-[75%]">
                  <div className="p-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                    <Bot className="w-4 h-4" />
                  </div>
                  <div className="px-4 py-3 rounded-lg bg-gray-50 dark:bg-gray-900">
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Analyzing...
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {error && (
              <div className="mx-6">
                <Alert variant="destructive">
                  <AlertDescription>
                    Unable to process your request. Please try again.
                  </AlertDescription>
                </Alert>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </div>

      {/* Input Area */}
      <div className="border-t border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900/50 p-4">
        <form onSubmit={handleFormSubmit}>
          <div className="flex gap-3">
            <Input
              value={input}
              onChange={handleInputChange}
              placeholder="Ask about budgeting, investments, debt management..."
              disabled={isLoading}
              className="flex-1 bg-white dark:bg-gray-950 border-gray-200 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400"
            />
            <Button
              type="submit"
              disabled={isLoading || !input.trim()}
              className="px-4 bg-blue-600 hover:bg-blue-700 text-white border-0"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
