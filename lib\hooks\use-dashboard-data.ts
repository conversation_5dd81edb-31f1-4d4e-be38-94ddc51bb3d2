"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useAuth } from "@/lib/contexts/auth-context";
import {
  getUserTransactions,
  getUserGoals,
  getUserRecurringPayments,
  getUserLoans,
  getMonthlyFinancialSummary,
} from "@/lib/supabase/queries";
import {
  Transaction,
  Goal,
  RecurringPayment,
  Loan,
} from "@/lib/types/database";

interface DashboardData {
  transactions: Transaction[];
  goals: Goal[];
  recurringPayments: RecurringPayment[];
  loans: Loan[];
  financialSummary: {
    income: number;
    expenses: number;
    balance: number;
  };
  // Add computed properties for compatibility
  totalGoalProgress?: number;
  totalDebt?: number;
  monthlyRecurringTotal?: number;
  recentTransactionsByType?: Record<string, number>;
  // Add compatibility properties for existing components
  balance?: number;
  income?: number;
  expenses?: number;
}

interface UseDashboardDataReturn {
  data: DashboardData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Cache for dashboard data to prevent unnecessary refetches
const dashboardCache = new Map<
  string,
  { data: DashboardData; timestamp: number }
>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export function useDashboardData(): UseDashboardDataReturn {
  const { user } = useAuth();
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Optimized data fetcher that batches all requests
  const fetchDashboardData = useCallback(async () => {
    if (!user?.id) {
      setData(null);
      setLoading(false);
      return;
    }

    // Check cache first
    const cacheKey = user.id;
    const cached = dashboardCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      setData(cached.data);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Batch all data fetching operations to prevent N+1 queries
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      // Execute all queries in parallel for better performance
      const [transactions, goals, recurringPayments, loans, financialSummary] =
        await Promise.all([
          getUserTransactions(user.id, 10).catch(() => []), // Limit to recent 10 transactions
          getUserGoals(user.id).catch(() => []),
          getUserRecurringPayments(user.id).catch(() => []),
          getUserLoans(user.id).catch(() => []),
          getMonthlyFinancialSummary(user.id, currentYear, currentMonth).catch(
            () => ({
              income: 0,
              expenses: 0,
              balance: 0,
            }),
          ),
        ]);

      const dashboardData: DashboardData = {
        transactions,
        goals,
        recurringPayments,
        loans,
        financialSummary,
      };

      // Cache the result
      dashboardCache.set(cacheKey, {
        data: dashboardData,
        timestamp: Date.now(),
      });

      setData(dashboardData);
    } catch (err) {
      console.error("Error fetching dashboard data:", err);
      setError(
        err instanceof Error ? err.message : "Failed to load dashboard data",
      );
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Memoized refetch function
  const refetch = useCallback(async () => {
    if (user?.id) {
      // Clear cache for this user
      dashboardCache.delete(user.id);
      await fetchDashboardData();
    }
  }, [user?.id, fetchDashboardData]);

  // Initial data fetch
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Memoized computed values to prevent unnecessary recalculations
  const memoizedData = useMemo(() => {
    if (!data) return null;

    return {
      ...data,
      // Add computed properties that are expensive to calculate
      totalGoalProgress:
        data.goals.reduce((sum, goal) => {
          return sum + (goal.current_amount / goal.target_amount) * 100;
        }, 0) / Math.max(data.goals.length, 1),

      totalDebt: data.loans.reduce(
        (sum, loan) => sum + loan.current_balance,
        0,
      ),

      monthlyRecurringTotal: data.recurringPayments
        .filter((payment) => payment.is_active)
        .reduce((sum, payment) => sum + payment.amount, 0),

      recentTransactionsByType: data.transactions.reduce(
        (acc, transaction) => {
          acc[transaction.type] = (acc[transaction.type] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      ),
      
      // Add compatibility properties for components that expect them directly
      balance: data.financialSummary.balance,
      income: data.financialSummary.income,
      expenses: data.financialSummary.expenses,
    };
  }, [data]);

  return {
    data: memoizedData,
    loading,
    error,
    refetch,
  };
}

// Hook for specific dashboard sections to prevent unnecessary re-renders
export function useDashboardSection<T extends keyof DashboardData>(
  section: T,
): {
  data: DashboardData[T] | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
} {
  const { data, loading, error, refetch } = useDashboardData();

  const sectionData = useMemo(() => {
    return data ? data[section] : null;
  }, [data, section]);

  return {
    data: sectionData,
    loading,
    error,
    refetch,
  };
}

// Utility to clear dashboard cache (useful for logout)
export function clearDashboardCache(userId?: string) {
  if (userId) {
    dashboardCache.delete(userId);
  } else {
    dashboardCache.clear();
  }
}

// Preload dashboard data (useful for navigation optimization)
export async function preloadDashboardData(userId: string) {
  const cacheKey = userId;
  const cached = dashboardCache.get(cacheKey);

  // Only preload if not already cached
  if (!cached || Date.now() - cached.timestamp >= CACHE_DURATION) {
    try {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      const [transactions, goals, recurringPayments, loans, financialSummary] =
        await Promise.all([
          getUserTransactions(userId, 10),
          getUserGoals(userId),
          getUserRecurringPayments(userId),
          getUserLoans(userId),
          getMonthlyFinancialSummary(userId, currentYear, currentMonth),
        ]);

      const dashboardData: DashboardData = {
        transactions,
        goals,
        recurringPayments,
        loans,
        financialSummary,
      };

      dashboardCache.set(cacheKey, {
        data: dashboardData,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error("Error preloading dashboard data:", error);
    }
  }
}
