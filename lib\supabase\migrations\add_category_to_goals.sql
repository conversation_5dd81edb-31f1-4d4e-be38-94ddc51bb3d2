-- Add category column to goals table
ALTER TABLE public.goals 
ADD COLUMN category VARCHAR(50) DEFAULT 'other';

-- Update existing goals to have a default category
UPDATE public.goals 
SET category = 'other' 
WHERE category IS NULL;

-- Make the column NOT NULL after setting defaults
ALTER TABLE public.goals 
ALTER COLUMN category SET NOT NULL;

-- Add a check constraint for valid categories
ALTER TABLE public.goals 
ADD CONSTRAINT valid_goal_category CHECK (
    category IN (
        'emergency_fund',
        'vacation',
        'house_down_payment',
        'car',
        'education',
        'retirement',
        'debt_payoff',
        'investment',
        'wedding',
        'other'
    )
);
