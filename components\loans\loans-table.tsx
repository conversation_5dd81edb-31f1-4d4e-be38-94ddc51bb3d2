"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/contexts/auth-context";
import { getUser<PERSON>oans, deleteLoan, update<PERSON>oan } from "@/lib/supabase/queries";
import { Loan } from "@/lib/types/database";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Plus,
  CreditCard,
  DollarSign,
  Percent,
  Calendar,
} from "lucide-react";
import { format, differenceInMonths } from "date-fns";

interface LoansTableProps {
  onEditLoan: (loan: Loan) => void;
  onAddLoan: () => void;
  refreshTrigger?: number;
  loanType: "bank_loan" | "personal_debt";
}

export function LoansTable({
  onEditLoan,
  onAddLoan,
  refreshTrigger,
  loanType,
}: LoansTableProps) {
  const { user, profile } = useAuth();
  const [loans, setLoans] = useState<Loan[]>([]);
  const [filteredLoans, setFilteredLoans] = useState<Loan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [loanToDelete, setLoanToDelete] = useState<Loan | null>(null);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null);
  const [paymentAmount, setPaymentAmount] = useState("");

  // Filter states
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    loadLoans();
  }, [user, refreshTrigger]);

  useEffect(() => {
    applyFilters();
  }, [loans, searchTerm, loanType]);

  const loadLoans = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const userLoans = await getUserLoans(user.id);
      setLoans(userLoans);
    } catch (err) {
      setError("Failed to load loans");
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = loans.filter((loan) => (loan.loan_type || loan.type) === loanType);

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (loan) =>
          loan.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (loan.category || "").toLowerCase().includes(searchTerm.toLowerCase()) ||
          ((loan.lender || loan.lender_name || "").toLowerCase().includes(searchTerm.toLowerCase())) ||
          (loan.description &&
            loan.description.toLowerCase().includes(searchTerm.toLowerCase())),
      );
    }

    // Sort by current balance (highest first)
    filtered.sort((a, b) => b.current_balance - a.current_balance);

    setFilteredLoans(filtered);
  };

  const handleDeleteLoan = async (loan: Loan) => {
    try {
      await deleteLoan(loan.id);
      await loadLoans();
      setDeleteDialogOpen(false);
      setLoanToDelete(null);
    } catch (err) {
      setError("Failed to delete loan");
    }
  };

  const handleMakePayment = async () => {
    if (!selectedLoan || !paymentAmount) return;

    try {
      const newBalance = Math.max(
        0,
        selectedLoan.current_balance - Number(paymentAmount),
      );
      await updateLoan(selectedLoan.id, { current_balance: newBalance });
      await loadLoans();
      setPaymentDialogOpen(false);
      setSelectedLoan(null);
      setPaymentAmount("");
    } catch (err) {
      setError("Failed to make payment");
      setError("Failed to record payment");
    }
  };

  const formatAmount = (amount: number) => {
    const currency = profile?.currency_code || "USD";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  const calculateProgress = (loan: Loan) => {
    const paidAmount = loan.principal_amount - loan.current_balance;
    return (paidAmount / loan.principal_amount) * 100;
  };

  const getMonthsRemaining = (loan: Loan) => {
    if (loanType === "personal_debt" || !loan.monthly_payment) return null;
    return Math.ceil(loan.current_balance / loan.monthly_payment);
  };

  const getTotalDebt = () => {
    return filteredLoans.reduce(
      (total, loan) => total + loan.current_balance,
      0,
    );
  };

  const getTotalMonthlyPayments = () => {
    return filteredLoans
      .filter((loan) => loan.monthly_payment)
      .reduce((total, loan) => total + (loan.monthly_payment || 0), 0);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">
              Loading {loanType === "bank_loan" ? "loans" : "debts"}...
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            {loanType === "bank_loan" ? "Bank Loans" : "Personal Debts"} Summary
          </CardTitle>
          <CardDescription>
            Overview of your{" "}
            {loanType === "bank_loan" ? "bank loans" : "personal debts"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <div className="text-sm text-muted-foreground">
                Total Outstanding
              </div>
              <div className="text-2xl font-bold text-red-600">
                {formatAmount(getTotalDebt())}
              </div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">
                Number of {loanType === "bank_loan" ? "Loans" : "Debts"}
              </div>
              <div className="text-2xl font-bold">{filteredLoans.length}</div>
            </div>
            {loanType === "bank_loan" && (
              <div>
                <div className="text-sm text-muted-foreground">
                  Monthly Payments
                </div>
                <div className="text-2xl font-bold text-orange-600">
                  {formatAmount(getTotalMonthlyPayments())}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div>
            <CardTitle>
              {loanType === "bank_loan" ? "Bank Loans" : "Personal Debts"}
            </CardTitle>
            <CardDescription>
              Manage your{" "}
              {loanType === "bank_loan"
                ? "bank loans and credit accounts"
                : "personal debts and informal loans"}
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent>
          {/* Search */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder={`Search ${loanType === "bank_loan" ? "loans" : "debts"}...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Table */}
          {filteredLoans.length === 0 ? (
            <div className="text-center py-12">
              <CreditCard className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">
                No {loanType === "bank_loan" ? "loans" : "debts"} found
              </h3>
              <p className="text-muted-foreground mb-4">
                Add your first {loanType === "bank_loan" ? "loan" : "debt"} to
                start tracking
              </p>
              <Button onClick={onAddLoan}>
                <Plus className="w-4 h-4 mr-2" />
                Add {loanType === "bank_loan" ? "Loan" : "Debt"}
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      {loanType === "bank_loan" ? "Loan" : "Debt"}
                    </TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Lender</TableHead>
                    <TableHead>Original Amount</TableHead>
                    <TableHead>Current Balance</TableHead>
                    <TableHead>Progress</TableHead>
                    {loanType === "bank_loan" && (
                      <TableHead>Monthly Payment</TableHead>
                    )}
                    {loanType === "bank_loan" && (
                      <TableHead>Interest Rate</TableHead>
                    )}
                    <TableHead className="w-12"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLoans.map((loan) => (
                    <TableRow key={loan.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{loan.name}</div>
                          {loan.description && (
                            <div className="text-sm text-muted-foreground">
                              {loan.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {loan.category.replace("_", " ")}
                        </Badge>
                      </TableCell>
                      <TableCell>{loan.lender || loan.lender_name || "Not specified"}</TableCell>
                      <TableCell className="font-mono">
                        {formatAmount(loan.principal_amount)}
                      </TableCell>
                      <TableCell className="font-mono font-medium">
                        {formatAmount(loan.current_balance)}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span>Paid</span>
                            <span>{calculateProgress(loan).toFixed(1)}%</span>
                          </div>
                          <Progress
                            value={calculateProgress(loan)}
                            className="h-2"
                          />
                        </div>
                      </TableCell>
                      {loanType === "bank_loan" && (
                        <TableCell className="font-mono">
                          {loan.monthly_payment
                            ? formatAmount(loan.monthly_payment)
                            : "N/A"}
                        </TableCell>
                      )}
                      {loanType === "bank_loan" && (
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Percent className="w-3 h-3" />
                            {loan.interest_rate?.toFixed(2) || "N/A"}
                          </div>
                        </TableCell>
                      )}
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onEditLoan(loan)}>
                              <Edit className="w-4 h-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedLoan(loan);
                                setPaymentDialogOpen(true);
                              }}
                            >
                              <DollarSign className="w-4 h-4 mr-2" />
                              Make Payment
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => {
                                setLoanToDelete(loan);
                                setDeleteDialogOpen(true);
                              }}
                              className="text-red-600"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Delete {loanType === "bank_loan" ? "Loan" : "Debt"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{loanToDelete?.name}&quot;? This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => loanToDelete && handleDeleteLoan(loanToDelete)}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Make Payment Dialog */}
      <Dialog open={paymentDialogOpen} onOpenChange={setPaymentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Make Payment</DialogTitle>
            <DialogDescription>
              Record a payment for &quot;{selectedLoan?.name}&quot;
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">
                Payment Amount ({profile?.currency_code || "USD"})
              </label>
              <Input
                type="number"
                step="0.01"
                min="0"
                max={selectedLoan?.current_balance}
                placeholder="0.00"
                value={paymentAmount}
                onChange={(e) => setPaymentAmount(e.target.value)}
                className="mt-1"
              />
            </div>

            {selectedLoan && paymentAmount && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="text-sm text-muted-foreground">
                  New balance will be:
                </div>
                <div className="font-medium">
                  {formatAmount(
                    Math.max(
                      0,
                      selectedLoan.current_balance - Number(paymentAmount),
                    ),
                  )}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setPaymentDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleMakePayment}
              disabled={
                !paymentAmount ||
                Number(paymentAmount) <= 0 ||
                Number(paymentAmount) > (selectedLoan?.current_balance || 0)
              }
            >
              Record Payment
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
