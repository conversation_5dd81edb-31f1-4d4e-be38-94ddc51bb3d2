import { render, screen, waitFor } from "@testing-library/react";
import { FinancialSummaryCards } from "@/components/dashboard/financial-summary-cards";
import { AuthProvider } from "@/lib/contexts/auth-context";
import { useDashboardData } from "@/lib/hooks/use-dashboard-data";

// Mock the dashboard data hook
jest.mock("@/lib/hooks/use-dashboard-data");
const mockUseDashboardData = useDashboardData as jest.MockedFunction<
  typeof useDashboardData
>;

// Mock the auth context
const mockUser = {
  id: "test-user-id",
  email: "<EMAIL>",
  created_at: new Date().toISOString(),
};

const mockProfile = {
  id: "test-profile-id",
  user_id: "test-user-id",
  full_name: "Test User",
  email: "<EMAIL>",
  currency_code: "USD",
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

const mockAuthContext = {
  user: mockUser,
  profile: mockProfile,
  session: null,
  loading: false,
  isOnboardingComplete: true,
  signIn: jest.fn(),
  signOut: jest.fn(),
  signUp: jest.fn(),
  updateProfile: jest.fn(),
  refreshProfile: jest.fn(),
};

// Mock the auth context hook
jest.mock("@/lib/contexts/auth-context", () => ({
  ...jest.requireActual("@/lib/contexts/auth-context"),
  useAuth: () => mockAuthContext,
}));

// Mock ErrorBoundary
jest.mock("@/components/error-boundary", () => ({
  ErrorBoundary: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}));

// Mock LoadingError component
jest.mock("@/components/error/error-messages", () => ({
  LoadingError: ({
    resource,
    onRetry,
  }: {
    resource: string;
    onRetry: () => void;
  }) => (
    <div data-testid="loading-error">
      <span>Error loading {resource}</span>
      <button onClick={onRetry}>Retry</button>
    </div>
  ),
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <AuthProvider>{component}</AuthProvider>,
  );
};

describe("FinancialSummaryCards", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Loading State", () => {
    it("displays loading skeletons when data is loading", () => {
      mockUseDashboardData.mockReturnValue({
        data: null,
        loading: true,
        error: null,
        refetch: jest.fn(),
      });

      renderWithProviders(<FinancialSummaryCards />);

      // Should show 3 loading cards
      const loadingCards = screen
        .getAllByRole("generic")
        .filter((el) => el.className.includes("animate-pulse"));
      expect(loadingCards.length).toBeGreaterThan(0);
    });

    it("shows proper loading structure with placeholders", () => {
      mockUseDashboardData.mockReturnValue({
        data: null,
        loading: true,
        error: null,
        refetch: jest.fn(),
      });

      renderWithProviders(<FinancialSummaryCards />);

      // Should have grid layout for loading cards
      const gridContainer = screen.getByRole("generic", {
        hidden: true, // Loading cards might be visually hidden to screen readers
      });
      expect(gridContainer).toBeInTheDocument();
    });
  });

  describe("Error State", () => {
    it("displays error message when data fetching fails", () => {
      const mockRefetch = jest.fn();
      mockUseDashboardData.mockReturnValue({
        data: null,
        loading: false,
        error: "Failed to fetch data",
        refetch: mockRefetch,
      });

      renderWithProviders(<FinancialSummaryCards />);

      expect(screen.getByTestId("loading-error")).toBeInTheDocument();
      expect(
        screen.getByText("Error loading financial summary"),
      ).toBeInTheDocument();
    });

    it("calls refetch when retry button is clicked", async () => {
      const mockRefetch = jest.fn();
      mockUseDashboardData.mockReturnValue({
        data: null,
        loading: false,
        error: "Failed to fetch data",
        refetch: mockRefetch,
      });

      renderWithProviders(<FinancialSummaryCards />);

      const retryButton = screen.getByText("Retry");
      retryButton.click();

      expect(mockRefetch).toHaveBeenCalledTimes(1);
    });

    it("shows sign-in message when user is not authenticated", () => {
      const mockAuthContextNoUser = {
        ...mockAuthContext,
        user: null,
        profile: null,
      };

      mockUseDashboardData.mockReturnValue({
        data: null,
        loading: false,
        error: "Not authenticated",
        refetch: jest.fn(),
      });

      // Temporarily update the mock for this test
      const originalMock = jest.requireMock("@/lib/contexts/auth-context").useAuth;
      jest.requireMock("@/lib/contexts/auth-context").useAuth = () => mockAuthContextNoUser;
      
      render(
        <AuthProvider>
          <FinancialSummaryCards />
        </AuthProvider>,
      );
      
      // Restore original mock
      jest.requireMock("@/lib/contexts/auth-context").useAuth = originalMock;

      expect(
        screen.getByText("Sign in to view your financial summary"),
      ).toBeInTheDocument();
    });
  });

  describe("Empty State", () => {
    it("displays empty state when no financial data is available", () => {
      mockUseDashboardData.mockReturnValue({
        data: {
          financialSummary: { income: 0, expenses: 0, balance: 0 },
          transactions: [],
          goals: [],
          recurringPayments: [],
          loans: [],
        },
        loading: false,
        error: null,
        refetch: jest.fn(),
      });

      renderWithProviders(<FinancialSummaryCards />);

      expect(screen.getByText("No financial data yet")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Start adding transactions to see your income, expenses, and balance",
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByText("Visit the Transactions page to get started"),
      ).toBeInTheDocument();
    });
  });

  describe("Data Display", () => {
    const mockFinancialData = {
      financialSummary: {
        income: 5500,
        expenses: 2100,
        balance: 3400,
      },
      transactions: [],
      goals: [],
      recurringPayments: [],
      loans: [],
    };

    it("displays financial data correctly", () => {
      mockUseDashboardData.mockReturnValue({
        data: mockFinancialData,
        loading: false,
        error: null,
        refetch: jest.fn(),
      });

      renderWithProviders(<FinancialSummaryCards />);

      expect(screen.getByText("Monthly Income")).toBeInTheDocument();
      expect(screen.getByText("Monthly Expenses")).toBeInTheDocument();
      expect(screen.getByText("Net Balance")).toBeInTheDocument();

      // Check formatted currency values
      expect(screen.getByText("$5,500.00")).toBeInTheDocument();
      expect(screen.getByText("$2,100.00")).toBeInTheDocument();
      expect(screen.getByText("$3,400.00")).toBeInTheDocument();
    });

    it("shows positive balance with correct styling", () => {
      mockUseDashboardData.mockReturnValue({
        data: mockFinancialData,
        loading: false,
        error: null,
        refetch: jest.fn(),
      });

      renderWithProviders(<FinancialSummaryCards />);

      const balanceElement = screen.getByText("$3,400.00");
      expect(balanceElement).toHaveClass("text-blue-600");

      expect(screen.getByText("Positive")).toBeInTheDocument();
      expect(
        screen.getByText("Healthy financial position"),
      ).toBeInTheDocument();
    });

    it("shows negative balance with correct styling", () => {
      const negativeBalanceData = {
        ...mockFinancialData,
        financialSummary: {
          income: 2000,
          expenses: 3000,
          balance: -1000,
        },
      };

      mockUseDashboardData.mockReturnValue({
        data: negativeBalanceData,
        loading: false,
        error: null,
        refetch: jest.fn(),
      });

      renderWithProviders(<FinancialSummaryCards />);

      const balanceElement = screen.getByText("-$1,000.00");
      expect(balanceElement).toHaveClass("text-orange-600");

      expect(screen.getByText("Negative")).toBeInTheDocument();
      expect(screen.getByText("Spending exceeds income")).toBeInTheDocument();
    });
  });

  describe("Currency Formatting", () => {
    it("formats currency according to user profile settings", () => {
      const eurProfile = { ...mockProfile, currency_code: "EUR" };
      const mockAuthContextEUR = { ...mockAuthContext, profile: eurProfile };

      mockUseDashboardData.mockReturnValue({
        data: {
          financialSummary: { income: 1000, expenses: 500, balance: 500 },
          transactions: [],
          goals: [],
          recurringPayments: [],
          loans: [],
        },
        loading: false,
        error: null,
        refetch: jest.fn(),
      });

      // Temporarily update the mock for this test
      const originalMock = jest.requireMock("@/lib/contexts/auth-context").useAuth;
      jest.requireMock("@/lib/contexts/auth-context").useAuth = () => mockAuthContextEUR;
      
      render(
        <AuthProvider>
          <FinancialSummaryCards />
        </AuthProvider>,
      );
      
      // Restore original mock
      jest.requireMock("@/lib/contexts/auth-context").useAuth = originalMock;

      // Should format as EUR currency
      expect(screen.getByText("€1,000.00")).toBeInTheDocument();
    });

    it("falls back to USD when currency_code is not set", () => {
      const profileNoCurrency = { ...mockProfile, currency_code: undefined };
      const mockAuthContextNoCurrency = {
        ...mockAuthContext,
        profile: profileNoCurrency,
      };

      mockUseDashboardData.mockReturnValue({
        data: {
          financialSummary: { income: 1000, expenses: 500, balance: 500 },
          transactions: [],
          goals: [],
          recurringPayments: [],
          loans: [],
        },
        loading: false,
        error: null,
        refetch: jest.fn(),
      });

      // Temporarily update the mock for this test
      const originalMock = jest.requireMock("@/lib/contexts/auth-context").useAuth;
      jest.requireMock("@/lib/contexts/auth-context").useAuth = () => mockAuthContextNoCurrency;
      
      render(
        <AuthProvider>
          <FinancialSummaryCards />
        </AuthProvider>,
      );
      
      // Restore original mock
      jest.requireMock("@/lib/contexts/auth-context").useAuth = originalMock;

      // Should default to USD
      expect(screen.getByText("$1,000.00")).toBeInTheDocument();
    });
  });

  describe("Percentage Changes", () => {
    it("calculates and displays percentage changes correctly", async () => {
      // Mock the previous month data loading
      const mockData = {
        financialSummary: { income: 5000, expenses: 2000, balance: 3000 },
        transactions: [],
        goals: [],
        recurringPayments: [],
        loans: [],
      };

      mockUseDashboardData.mockReturnValue({
        data: mockData,
        loading: false,
        error: null,
        refetch: jest.fn(),
      });

      renderWithProviders(<FinancialSummaryCards />);

      // Wait for component to load previous month data
      await waitFor(() => {
        // Should show percentage change badges
        const badges = screen
          .getAllByRole("generic")
          .filter(
            (el) =>
              el.className.includes("badge") || el.textContent?.includes("%"),
          );
        expect(badges.length).toBeGreaterThan(0);
      });
    });
  });

  describe("Accessibility", () => {
    it("has proper ARIA labels and roles", () => {
      mockUseDashboardData.mockReturnValue({
        data: {
          financialSummary: { income: 5000, expenses: 2000, balance: 3000 },
          transactions: [],
          goals: [],
          recurringPayments: [],
          loans: [],
        },
        loading: false,
        error: null,
        refetch: jest.fn(),
      });

      renderWithProviders(<FinancialSummaryCards />);

      // Check for proper heading hierarchy
      expect(screen.getByText("Monthly Income")).toBeInTheDocument();
      expect(screen.getByText("Monthly Expenses")).toBeInTheDocument();
      expect(screen.getByText("Net Balance")).toBeInTheDocument();

      // Check for descriptive text
      expect(screen.getByText("Compared to last month")).toBeInTheDocument();
      expect(screen.getByText("Income minus expenses")).toBeInTheDocument();
    });

    it("provides meaningful text for screen readers", () => {
      mockUseDashboardData.mockReturnValue({
        data: {
          financialSummary: { income: 5000, expenses: 2000, balance: 3000 },
          transactions: [],
          goals: [],
          recurringPayments: [],
          loans: [],
        },
        loading: false,
        error: null,
        refetch: jest.fn(),
      });

      renderWithProviders(<FinancialSummaryCards />);

      // Should have descriptive card content
      expect(screen.getByText("Income minus expenses")).toBeInTheDocument();
      expect(screen.getByText("Compared to last month")).toBeInTheDocument();
    });
  });

  describe("Error Boundary Integration", () => {
    it("is wrapped with ErrorBoundary", () => {
      mockUseDashboardData.mockReturnValue({
        data: {
          financialSummary: { income: 5000, expenses: 2000, balance: 3000 },
          transactions: [],
          goals: [],
          recurringPayments: [],
          loans: [],
        },
        loading: false,
        error: null,
        refetch: jest.fn(),
      });

      renderWithProviders(<FinancialSummaryCards />);

      // Component should render without errors
      expect(screen.getByText("Monthly Income")).toBeInTheDocument();
    });
  });
});
