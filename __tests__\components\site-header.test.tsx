import { render, screen } from "@testing-library/react";
import { SiteHeader } from "@/components/site-header";
import { SidebarProvider } from "@/components/ui/sidebar";

// Mock next/navigation
jest.mock("next/navigation", () => ({
  usePathname: jest.fn(() => "/dashboard"),
}));

describe("SiteHeader", () => {
  const renderWithProvider = (component: React.ReactElement) => {
    return render(<SidebarProvider>{component}</SidebarProvider>);
  };

  it("renders the site title", () => {
    renderWithProvider(<SiteHeader />);

    // Should show "Finance Tracker" as the site title
    expect(screen.getByText("Finance Tracker")).toBeInTheDocument();
  });

  it("renders the sidebar trigger button", () => {
    renderWithProvider(<SiteHeader />);

    // Should have a button element (sidebar trigger)
    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
  });
});
