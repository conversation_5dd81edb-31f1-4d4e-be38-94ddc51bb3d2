"use client";

import { useState } from "react";
import { useAuth } from "@/lib/contexts/auth-context";
import { create<PERSON>oan, update<PERSON>oan } from "@/lib/supabase/queries";
import { Loan } from "@/lib/types/database";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CreditCard, Loader2, Calculator } from "lucide-react";
import { format, addMonths } from "date-fns";
import { log } from "@/lib/utils/logger";

interface LoanFormProps {
  loan?: Loan;
  onSuccess: () => void;
  onCancel: () => void;
  loanType: "bank_loan" | "personal_debt";
}

const loanCategories = {
  bank_loan: [
    { value: "mortgage", label: "Mortgage" },
    { value: "auto_loan", label: "Auto Loan" },
    { value: "personal_loan", label: "Personal Loan" },
    { value: "student_loan", label: "Student Loan" },
    { value: "business_loan", label: "Business Loan" },
    { value: "credit_card", label: "Credit Card" },
    { value: "other", label: "Other" },
  ],
  personal_debt: [
    { value: "family", label: "Family Member" },
    { value: "friend", label: "Friend" },
    { value: "colleague", label: "Colleague" },
    { value: "other", label: "Other" },
  ],
};

export function LoanForm({
  loan,
  onSuccess,
  onCancel,
  loanType,
}: LoanFormProps) {
  const { user, profile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    name: loan?.name || "",
    principal_amount: loan?.principal_amount?.toString() || "",
    current_balance: loan?.current_balance?.toString() || "",
    interest_rate: loan?.interest_rate?.toString() || "",
    term_months: loan?.term_months?.toString() || "",
    monthly_payment: loan?.monthly_payment?.toString() || "",
    start_date: loan?.start_date || format(new Date(), "yyyy-MM-dd"),
    description: loan?.description || "",
    category: loan?.category || "",
    lender_name: loan?.lender_name || "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setError("");
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
    setError("");
  };

  const calculateMonthlyPayment = () => {
    const principal = Number(formData.principal_amount);
    const rate = Number(formData.interest_rate) / 100 / 12;
    const months = Number(formData.term_months);

    if (principal && rate && months) {
      const monthlyPayment =
        (principal * (rate * Math.pow(1 + rate, months))) /
        (Math.pow(1 + rate, months) - 1);
      setFormData((prev) => ({
        ...prev,
        monthly_payment: monthlyPayment.toFixed(2),
      }));
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError("Please enter a loan/debt name");
      return false;
    }

    if (
      !formData.principal_amount ||
      isNaN(Number(formData.principal_amount)) ||
      Number(formData.principal_amount) <= 0
    ) {
      setError("Please enter a valid principal amount greater than 0");
      return false;
    }

    if (
      !formData.current_balance ||
      isNaN(Number(formData.current_balance)) ||
      Number(formData.current_balance) < 0
    ) {
      setError("Please enter a valid current balance (0 or greater)");
      return false;
    }

    if (Number(formData.current_balance) > Number(formData.principal_amount)) {
      setError("Current balance cannot be greater than principal amount");
      return false;
    }

    if (loanType === "bank_loan") {
      if (
        !formData.interest_rate ||
        isNaN(Number(formData.interest_rate)) ||
        Number(formData.interest_rate) < 0
      ) {
        setError("Please enter a valid interest rate (0 or greater)");
        return false;
      }

      if (
        !formData.term_months ||
        isNaN(Number(formData.term_months)) ||
        Number(formData.term_months) <= 0
      ) {
        setError("Please enter a valid term in months");
        return false;
      }

      if (
        !formData.monthly_payment ||
        isNaN(Number(formData.monthly_payment)) ||
        Number(formData.monthly_payment) <= 0
      ) {
        setError("Please enter a valid monthly payment amount");
        return false;
      }
    }

    if (!formData.category) {
      setError("Please select a category");
      return false;
    }

    if (!formData.start_date) {
      setError("Please select a start date");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !user) return;

    setLoading(true);
    setError("");

    try {
      const loanData = {
        user_id: user.id,
        name: formData.name.trim(),
        type: loanType, // Use 'type' field as required by database
        principal_amount: Number(formData.principal_amount),
        current_balance: Number(formData.current_balance),
        interest_rate:
          loanType === "bank_loan" ? Number(formData.interest_rate) : 0,
        term_months:
          loanType === "bank_loan" ? Number(formData.term_months) : null,
        monthly_payment:
          loanType === "bank_loan" ? Number(formData.monthly_payment) : null,
        start_date: formData.start_date,
        end_date: null, // Add required end_date field
        description: formData.description.trim(),
        category: formData.category,
        lender_name: formData.lender_name.trim(),
        currency_code: profile?.currency_code || "USD",
      };

      if (loan) {
        await updateLoan(loan.id, loanData);
      } else {
        await createLoan(loanData);
      }

      onSuccess();
    } catch (err) {
      log.error("Error saving loan", err, "LOAN_FORM");
      setError("Failed to save loan. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const categories = loanCategories[loanType];

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="w-5 h-5" />
          {loan
            ? `Edit ${loanType === "bank_loan" ? "Loan" : "Debt"}`
            : `Add New ${loanType === "bank_loan" ? "Loan" : "Debt"}`}
        </CardTitle>
        <CardDescription>
          {loan
            ? "Update loan/debt details"
            : `Add a new ${loanType === "bank_loan" ? "bank loan" : "personal debt"} to track`}
        </CardDescription>
      </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Name */}
          <div className="space-y-2">
            <Label htmlFor="name">
              {loanType === "bank_loan" ? "Loan" : "Debt"} Name *
            </Label>
            <Input
              id="name"
              name="name"
              placeholder={
                loanType === "bank_loan"
                  ? "e.g., Home Mortgage, Car Loan"
                  : "e.g., Money borrowed from John"
              }
              value={formData.name}
              onChange={handleInputChange}
            />
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Category *</Label>
            <Select
              value={formData.category}
              onValueChange={(value) => handleSelectChange("category", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Lender */}
          <div className="space-y-2">
            <Label htmlFor="lender_name">
              {loanType === "bank_loan" ? "Lender/Bank" : "Person/Entity"}
            </Label>
            <Input
              id="lender_name"
              name="lender_name"
              placeholder={
                loanType === "bank_loan"
                  ? "e.g., Chase Bank, Wells Fargo"
                  : "e.g., John Smith, Family Member"
              }
              value={formData.lender_name}
              onChange={handleInputChange}
            />
          </div>

          {/* Principal Amount */}
          <div className="space-y-2">
            <Label htmlFor="principal_amount">
              Original Amount ({profile?.currency_code || "USD"}) *
            </Label>
            <Input
              id="principal_amount"
              name="principal_amount"
              type="number"
              step="0.01"
              min="0"
              placeholder="10000.00"
              value={formData.principal_amount}
              onChange={handleInputChange}
              className="text-lg"
            />
          </div>

          {/* Current Balance */}
          <div className="space-y-2">
            <Label htmlFor="current_balance">
              Current Balance ({profile?.currency_code || "USD"}) *
            </Label>
            <Input
              id="current_balance"
              name="current_balance"
              type="number"
              step="0.01"
              min="0"
              placeholder="8500.00"
              value={formData.current_balance}
              onChange={handleInputChange}
              className="text-lg"
            />
          </div>

          {/* Bank Loan Specific Fields */}
          {loanType === "bank_loan" && (
            <>
              {/* Interest Rate */}
              <div className="space-y-2">
                <Label htmlFor="interest_rate">
                  Interest Rate (% per year) *
                </Label>
                <Input
                  id="interest_rate"
                  name="interest_rate"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  placeholder="5.25"
                  value={formData.interest_rate}
                  onChange={handleInputChange}
                />
              </div>

              {/* Term */}
              <div className="space-y-2">
                <Label htmlFor="term_months">Loan Term (months) *</Label>
                <Input
                  id="term_months"
                  name="term_months"
                  type="number"
                  min="1"
                  placeholder="360"
                  value={formData.term_months}
                  onChange={handleInputChange}
                />
              </div>

              {/* Monthly Payment with Calculator */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="monthly_payment">
                    Monthly Payment ({profile?.currency_code || "USD"}) *
                  </Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={calculateMonthlyPayment}
                    disabled={
                      !formData.principal_amount ||
                      !formData.interest_rate ||
                      !formData.term_months
                    }
                  >
                    <Calculator className="w-4 h-4 mr-2" />
                    Calculate
                  </Button>
                </div>
                <Input
                  id="monthly_payment"
                  name="monthly_payment"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="536.82"
                  value={formData.monthly_payment}
                  onChange={handleInputChange}
                  className="text-lg"
                />
              </div>
            </>
          )}

          {/* Start Date */}
          <div className="space-y-2">
            <Label htmlFor="start_date">Start Date *</Label>
            <Input
              id="start_date"
              name="start_date"
              type="date"
              value={formData.start_date}
              onChange={handleInputChange}
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Optional notes about this loan/debt..."
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
            />
          </div>

          {/* Loan Summary */}
          {loanType === "bank_loan" &&
            formData.principal_amount &&
            formData.current_balance && (
              <div className="p-4 bg-muted rounded-lg space-y-2">
                <div className="font-medium">Loan Summary</div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Amount Paid:</span>
                    <div className="font-medium">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: profile?.currency_code || "USD",
                      }).format(
                        Number(formData.principal_amount) -
                          Number(formData.current_balance),
                      )}
                    </div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Remaining:</span>
                    <div className="font-medium">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: profile?.currency_code || "USD",
                      }).format(Number(formData.current_balance))}
                    </div>
                  </div>
                </div>
              </div>
            )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {loan ? "Update" : "Add"}{" "}
              {loanType === "bank_loan" ? "Loan" : "Debt"}
            </Button>
          </div>
        </CardContent>
      </form>
    </Card>
  );
}
