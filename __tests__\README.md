# Testing Setup

This directory contains the test suite for the Finance Tracker application.

## Setup

The project uses:

- **Jest** as the test runner
- **React Testing Library** for component testing
- **@testing-library/jest-dom** for additional matchers

## Configuration

- `jest.config.js` - Main Jest configuration
- `jest.setup.js` - Test setup and global mocks

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

## Test Structure

```
__tests__/
├── app/                    # Page component tests
│   └── dashboard/
├── components/             # Component tests
│   ├── ui/                # UI component tests
│   └── [component-name]/
├── lib/                   # Utility function tests
└── README.md              # This file
```

## Writing Tests

### Component Tests

```javascript
import { render, screen } from "@testing-library/react";
import { MyComponent } from "@/components/my-component";

describe("MyComponent", () => {
  it("renders correctly", () => {
    render(<MyComponent />);
    expect(screen.getByText("Expected Text")).toBeInTheDocument();
  });
});
```

### Utility Tests

```javascript
import { myUtilFunction } from "@/lib/utils";

describe("myUtilFunction", () => {
  it("returns expected result", () => {
    expect(myUtilFunction("input")).toBe("expected output");
  });
});
```

## Mocking

The setup includes global mocks for:

- Next.js navigation (`next/navigation`)
- Next.js Image component (`next/image`)
- Supabase client
- Browser APIs (matchMedia, IntersectionObserver, ResizeObserver)

## Current Test Coverage

- ✅ Basic UI components (Button)
- ✅ Layout components (SiteHeader)
- ✅ Utility functions (utils)
- ✅ Page components (Dashboard)

## Next Steps

1. Add tests for dashboard components
2. Add tests for transaction components
3. Add integration tests for key user flows
4. Set up E2E testing with Playwright
5. Add visual regression testing
