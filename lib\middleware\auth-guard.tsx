"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/contexts/auth-context";

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

export function AuthGuard({
  children,
  requireAuth = true,
  redirectTo = "/auth/signin",
}: AuthGuardProps) {
  const { user, loading, isOnboardingComplete } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (requireAuth && !user) {
        router.push(redirectTo);
      } else if (!requireAuth && user) {
        // If user is authenticated but accessing auth pages, redirect based on onboarding status
        if (isOnboardingComplete) {
          router.push("/dashboard");
        } else {
          router.push("/onboarding");
        }
      } else if (
        requireAuth &&
        user &&
        !isOnboardingComplete &&
        typeof window !== "undefined" &&
        !window.location.pathname.startsWith("/onboarding")
      ) {
        // If user is authenticated but hasn't completed onboarding and not on onboarding page
        router.push("/onboarding");
      }
    }
  }, [user, loading, requireAuth, redirectTo, router, isOnboardingComplete]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // If auth is required but user is not authenticated, don't render children
  if (requireAuth && !user) {
    return null;
  }

  // If auth is not required but user is authenticated, don't render children
  if (!requireAuth && user) {
    return null;
  }

  return <>{children}</>;
}
