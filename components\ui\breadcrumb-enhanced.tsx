"use client";

import * as React from "react";
import { ChevronRight, Home } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { EnhancedTooltip } from "./enhanced-tooltip";

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
  tooltip?: string;
}

interface EnhancedBreadcrumbProps {
  items?: BreadcrumbItem[];
  separator?: React.ReactNode;
  maxItems?: number;
  className?: string;
  showHome?: boolean;
}

function EnhancedBreadcrumb({
  items = [],
  separator = <ChevronRight className="h-4 w-4" />,
  maxItems = 5,
  className,
  showHome = true,
}: EnhancedBreadcrumbProps) {
  const pathname = usePathname();

  // Auto-generate breadcrumbs from pathname if no items provided
  const breadcrumbItems = React.useMemo(() => {
    if (items.length > 0) return items;

    const pathSegments = pathname.split("/").filter(Boolean);
    const autoItems: BreadcrumbItem[] = [];

    if (showHome) {
      autoItems.push({
        label: "Home",
        href: "/",
        icon: <Home className="h-4 w-4" />,
        tooltip: "Go to homepage",
      });
    }

    pathSegments.forEach((segment, index) => {
      const href = "/" + pathSegments.slice(0, index + 1).join("/");
      const label = segment
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");

      autoItems.push({
        label,
        href,
        tooltip: `Go to ${label}`,
      });
    });

    return autoItems;
  }, [items, pathname, showHome]);

  // Truncate items if they exceed maxItems
  const displayItems = React.useMemo(() => {
    if (breadcrumbItems.length <= maxItems) return breadcrumbItems;

    const firstItem = breadcrumbItems[0];
    const lastItems = breadcrumbItems.slice(-2);

    return [firstItem, { label: "...", tooltip: "More pages" }, ...lastItems];
  }, [breadcrumbItems, maxItems]);

  return (
    <nav
      aria-label="Breadcrumb"
      className={cn("flex items-center space-x-1 text-sm", className)}
    >
      <ol className="flex items-center space-x-1">
        {displayItems.map((item, index) => {
          const isLast = index === displayItems.length - 1;
          const isEllipsis = item.label === "...";

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <span className="mx-2 text-muted-foreground">{separator}</span>
              )}

              {isEllipsis ? (
                <EnhancedTooltip content={item.tooltip || ""}>
                  <span className="text-muted-foreground px-2 py-1 rounded hover:bg-accent transition-colors">
                    {item.label}
                  </span>
                </EnhancedTooltip>
              ) : isLast ? (
                <span
                  className="font-medium text-foreground flex items-center gap-1"
                  aria-current="page"
                >
                  {item.icon}
                  {item.label}
                </span>
              ) : (
                <EnhancedTooltip content={item.tooltip || ""}>
                  <Link
                    href={item.href || "#"}
                    className="text-muted-foreground hover:text-foreground transition-colors px-2 py-1 rounded hover:bg-accent flex items-center gap-1 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                  >
                    {item.icon}
                    {item.label}
                  </Link>
                </EnhancedTooltip>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

export { EnhancedBreadcrumb, type BreadcrumbItem };
