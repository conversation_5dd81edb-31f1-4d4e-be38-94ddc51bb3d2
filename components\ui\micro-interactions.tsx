"use client";

import * as React from "react";
import { motion, useAnimation, useInView } from "framer-motion";
import { cn } from "@/lib/utils";

interface HoverScaleProps {
  children: React.ReactNode;
  scale?: number;
  className?: string;
}

function HoverScale({ children, scale = 1.05, className }: HoverScaleProps) {
  return (
    <motion.div
      whileHover={{ scale }}
      whileTap={{ scale: scale * 0.95 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface FloatingProps {
  children: React.ReactNode;
  duration?: number;
  distance?: number;
  className?: string;
}

function Floating({
  children,
  duration = 3,
  distance = 10,
  className,
}: FloatingProps) {
  return (
    <motion.div
      animate={{
        y: [-distance, distance, -distance],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: "easeInOut",
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface PulseProps {
  children: React.ReactNode;
  scale?: number;
  duration?: number;
  className?: string;
}

function Pulse({ children, scale = 1.1, duration = 1, className }: PulseProps) {
  return (
    <motion.div
      animate={{
        scale: [1, scale, 1],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: "easeInOut",
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface ShakeProps {
  children: React.ReactNode;
  trigger?: boolean;
  intensity?: number;
  className?: string;
}

function Shake({
  children,
  trigger = false,
  intensity = 10,
  className,
}: ShakeProps) {
  const controls = useAnimation();

  React.useEffect(() => {
    if (trigger) {
      controls.start({
        x: [-intensity, intensity, -intensity, intensity, 0],
        transition: { duration: 0.5 },
      });
    }
  }, [trigger, controls, intensity]);

  return (
    <motion.div animate={controls} className={className}>
      {children}
    </motion.div>
  );
}

interface SuccessCheckmarkProps {
  size?: number;
  className?: string;
  color?: string;
}

function SuccessCheckmark({
  size = 24,
  className,
  color = "#22c55e",
}: SuccessCheckmarkProps) {
  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      className={cn("flex items-center justify-center", className)}
    >
      <motion.svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <motion.path
          d="M20 6L9 17l-5-5"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        />
      </motion.svg>
    </motion.div>
  );
}

interface TypewriterProps {
  text: string;
  speed?: number;
  className?: string;
  onComplete?: () => void;
}

function Typewriter({
  text,
  speed = 50,
  className,
  onComplete,
}: TypewriterProps) {
  const [displayText, setDisplayText] = React.useState("");
  const [currentIndex, setCurrentIndex] = React.useState(0);

  React.useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayText((prev) => prev + text[currentIndex]);
        setCurrentIndex((prev) => prev + 1);
      }, speed);

      return () => clearTimeout(timer);
    } else if (onComplete) {
      onComplete();
    }
  }, [currentIndex, text, speed, onComplete]);

  return (
    <span className={className}>
      {displayText}
      <motion.span
        animate={{ opacity: [1, 0] }}
        transition={{ duration: 0.8, repeat: Infinity }}
        className="inline-block w-0.5 h-5 bg-current ml-1"
      />
    </span>
  );
}

interface ProgressRingProps {
  progress: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
  showPercentage?: boolean;
}

function ProgressRing({
  progress,
  size = 60,
  strokeWidth = 4,
  className,
  showPercentage = true,
}: ProgressRingProps) {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div
      className={cn("relative", className)}
      style={{ width: size, height: size }}
    >
      <svg width={size} height={size} className="transform -rotate-90">
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-muted"
          opacity={0.2}
        />
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={{ duration: 1, ease: "easeInOut" }}
          className="text-primary"
          strokeLinecap="round"
        />
      </svg>
      {showPercentage && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-medium">{Math.round(progress)}%</span>
        </div>
      )}
    </div>
  );
}

interface MorphingShapeProps {
  shapes: string[];
  duration?: number;
  className?: string;
}

function MorphingShape({
  shapes,
  duration = 2,
  className,
}: MorphingShapeProps) {
  const [currentShapeIndex, setCurrentShapeIndex] = React.useState(0);

  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentShapeIndex((prev) => (prev + 1) % shapes.length);
    }, duration * 1000);

    return () => clearInterval(interval);
  }, [shapes.length, duration]);

  return (
    <svg viewBox="0 0 100 100" className={className}>
      <motion.path
        d={shapes[currentShapeIndex]}
        fill="currentColor"
        animate={{ d: shapes[currentShapeIndex] }}
        transition={{ duration, ease: "easeInOut" }}
      />
    </svg>
  );
}

interface CounterProps {
  from: number;
  to: number;
  duration?: number;
  className?: string;
  prefix?: string;
  suffix?: string;
}

function Counter({
  from,
  to,
  duration = 1,
  className,
  prefix = "",
  suffix = "",
}: CounterProps) {
  const [count, setCount] = React.useState(from);
  const ref = React.useRef<HTMLSpanElement>(null);
  const inView = useInView(ref, { once: true });

  React.useEffect(() => {
    if (inView) {
      const startTime = Date.now();
      const endTime = startTime + duration * 1000;

      const updateCount = () => {
        const now = Date.now();
        const progress = Math.min((now - startTime) / (endTime - startTime), 1);

        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentCount = from + (to - from) * easeOutQuart;

        setCount(currentCount);

        if (progress < 1) {
          requestAnimationFrame(updateCount);
        }
      };

      requestAnimationFrame(updateCount);
    }
  }, [from, to, duration, inView]);

  return (
    <span ref={ref} className={className}>
      {prefix}
      {Math.round(count)}
      {suffix}
    </span>
  );
}

export {
  HoverScale,
  Floating,
  Pulse,
  Shake,
  SuccessCheckmark,
  Typewriter,
  ProgressRing,
  MorphingShape,
  Counter,
};
