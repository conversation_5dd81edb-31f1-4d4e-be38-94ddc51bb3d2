/**
 * Utility functions for handling user profile data
 */

import { UserProfile } from "@/lib/types/database";

/**
 * Checks if a full name is actually an email address
 */
export function isEmailLikeString(str: string): boolean {
  if (!str) return false;
  return str.includes("@") && str.includes(".");
}

/**
 * Cleans up profile data to ensure full_name is not an email
 */
export function cleanProfileData(
  profile: UserProfile | null,
  userEmail?: string,
): UserProfile | null {
  if (!profile) return null;

  const cleanedProfile = { ...profile };

  // If full_name is the same as the user's email, clear it
  if (cleanedProfile.full_name === userEmail) {
    cleanedProfile.full_name = null;
  }

  // If full_name looks like an email, clear it
  if (cleanedProfile.full_name && isEmailLikeString(cleanedProfile.full_name)) {
    cleanedProfile.full_name = null;
  }

  return cleanedProfile;
}

/**
 * Gets a clean full name that's not an email address
 */
export function getCleanFullName(
  profile: UserProfile | null,
  userEmail?: string,
): string {
  if (!profile?.full_name) return "";

  // If full_name is the same as email, return empty string
  if (profile.full_name === userEmail) return "";

  // If full_name looks like an email, return empty string
  if (isEmailLikeString(profile.full_name)) return "";

  return profile.full_name;
}

/**
 * Validates that a full name is not an email address
 */
export function validateFullName(
  fullName: string,
  userEmail?: string,
): { valid: boolean; message?: string } {
  if (!fullName.trim()) {
    return { valid: false, message: "Full name is required" };
  }

  if (fullName === userEmail) {
    return {
      valid: false,
      message: "Full name cannot be the same as your email address",
    };
  }

  if (isEmailLikeString(fullName)) {
    return { valid: false, message: "Full name cannot be an email address" };
  }

  if (fullName.length < 2) {
    return {
      valid: false,
      message: "Full name must be at least 2 characters long",
    };
  }

  if (fullName.length > 100) {
    return {
      valid: false,
      message: "Full name must be less than 100 characters",
    };
  }

  return { valid: true };
}

/**
 * Gets initials from a full name
 */
export function getInitials(fullName: string): string {
  if (!fullName) return "";

  return fullName
    .split(" ")
    .map((word) => word.charAt(0))
    .join("")
    .toUpperCase()
    .slice(0, 2);
}

/**
 * Formats a display name from profile data
 */
export function getDisplayName(
  profile: UserProfile | null,
  userEmail?: string,
): string {
  const cleanName = getCleanFullName(profile, userEmail);

  if (cleanName) {
    return cleanName;
  }

  // Fallback to email username if no clean full name
  if (userEmail) {
    return userEmail.split("@")[0];
  }

  return "User";
}
