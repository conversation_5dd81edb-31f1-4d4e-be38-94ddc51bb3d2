"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { Session, AuthError } from "@supabase/supabase-js";
import { supabase } from "@/lib/supabase/client";
import { UserProfile, DbUser } from "@/lib/types/database";
import { getUserProfile } from "@/lib/supabase/queries";
import { cleanProfileData } from "@/lib/utils/profile-utils";
import { log } from "@/lib/utils/logger";

interface AuthContextType {
  user: DbUser | null;
  profile: UserProfile | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
  isOnboardingComplete: boolean;
  signUp: (
    email: string,
    password: string,
    fullName: string,
  ) => Promise<{ error: AuthError | null }>;
  signIn: (
    email: string,
    password: string,
  ) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  refreshProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<DbUser | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if onboarding is complete
  const isOnboardingComplete = Boolean(
    profile?.full_name &&
      profile?.currency_code &&
      profile?.salary_payment_date,
  );

  const loadUserProfileCallback = useCallback(async (userId: string) => {
    try {
      const userProfile = await getUserProfile(userId);

      // Log the profile data for debugging
      log.dev("Loaded user profile", userProfile, "AUTH");

      // Clean the profile data to ensure full_name is not an email
      const cleanedProfile = cleanProfileData(userProfile, user?.email);
      log.dev("Cleaned user profile", cleanedProfile ? { ...cleanedProfile } : {}, "AUTH");

      setProfile(cleanedProfile);
    } catch (error) {
      log.warn("Error loading user profile", error, "AUTH");

      // If profile doesn't exist, create a basic one
      try {
        const { createUserProfile } = await import("@/lib/supabase/queries");
        const newProfile = await createUserProfile(userId, {});
        log.info("Created new user profile", newProfile, "AUTH");

        // Clean the new profile as well
        const cleanedNewProfile = cleanProfileData(newProfile, user?.email);
        setProfile(cleanedNewProfile);
      } catch (createError) {
        log.error("Error creating user profile", createError, "AUTH");
        setProfile(null);
      }
    }
  }, [user?.email]);

  useEffect(() => {
    let isMounted = true;

    // Get initial session
    const getInitialSession = async () => {
      try {
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (!isMounted) return;

        if (error) {
          log.error("Error getting session", error, "AUTH");
          setError(error.message);
        } else {
          setError(null);
          setSession(session);
          setUser(session?.user ?? null);

          if (session?.user) {
            await loadUserProfileCallback(session.user.id);
          }
        }

        setLoading(false);
      } catch (error) {
        if (isMounted) {
          log.error("Error in getInitialSession", error, "AUTH");
          setError("Failed to initialize session");
          setLoading(false);
        }
      }
    };

    getInitialSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!isMounted) return;

      log.dev("Auth state changed", { event, userId: session?.user?.id }, "AUTH");

      setSession(session);
      setUser(session?.user ?? null);

      try {
        if (session?.user) {
          await loadUserProfileCallback(session.user.id);
        } else {
          setProfile(null);
        }
      } catch (error) {
        log.error("Error in auth state change", error, "AUTH");
        setError("Failed to update profile");
      }

      setLoading(false);
    });

    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, [loadUserProfileCallback]);



  const signUp = async (email: string, password: string, fullName: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    });

    if (error) {
      console.error("Sign up error:", error);
      return { error };
    }

    return { error: null };
  };

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error("Sign in error:", error);
      return { error };
    }

    return { error: null };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error("Sign out error:", error);
      return { error };
    }

    setUser(null);
    setProfile(null);
    setSession(null);

    return { error: null };
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) throw new Error("No user logged in");

    try {
      const { updateUserProfile } = await import("@/lib/supabase/queries");
      const updatedProfile = await updateUserProfile(user.id, updates);
      setProfile(updatedProfile);
    } catch (error) {
      console.error("Error updating profile:", error);
      throw error;
    }
  };

  const refreshProfile = async () => {
    if (!user) return;
    await loadUserProfile(user.id);
  };

  const value: AuthContextType = {
    user,
    profile,
    session,
    loading,
    error,
    isOnboardingComplete,
    signUp,
    signIn,
    signOut,
    updateProfile,
    refreshProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
