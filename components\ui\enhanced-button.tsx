"use client";

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const enhancedButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 ease-out disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive relative overflow-hidden group",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] active:shadow-sm",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80 hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",
        ghost:
          "hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",
        link: "text-primary underline-offset-4 hover:underline hover:text-primary/80",
        success:
          "bg-success text-success-foreground shadow-xs hover:bg-success/90 hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",
        warning:
          "bg-warning text-warning-foreground shadow-xs hover:bg-warning/90 hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        xl: "h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",
        icon: "size-9",
      },
      loading: {
        true: "cursor-not-allowed",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      loading: false,
    },
  },
);

// Ripple effect component
const Ripple = ({ x, y }: { x: number; y: number }) => (
  <span
    className="absolute rounded-full bg-white/30 animate-ping"
    style={{
      left: x - 10,
      top: y - 10,
      width: 20,
      height: 20,
    }}
  />
);

interface EnhancedButtonProps
  extends React.ComponentProps<"button">,
    VariantProps<typeof enhancedButtonVariants> {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
  ripple?: boolean;
  success?: boolean;
  successText?: string;
  successDuration?: number;
}

function EnhancedButton({
  className,
  variant,
  size,
  asChild = false,
  loading = false,
  loadingText,
  ripple = true,
  success = false,
  successText,
  successDuration = 2000,
  children,
  onClick,
  ...props
}: EnhancedButtonProps) {
  const [ripples, setRipples] = React.useState<
    Array<{ x: number; y: number; id: number }>
  >([]);
  const [isSuccess, setIsSuccess] = React.useState(false);
  const buttonRef = React.useRef<HTMLButtonElement>(null);

  const Comp = asChild ? Slot : "button";

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (loading || isSuccess) return;

    // Add ripple effect
    if (ripple && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const newRipple = { x, y, id: Date.now() };

      setRipples((prev) => [...prev, newRipple]);

      // Remove ripple after animation
      setTimeout(() => {
        setRipples((prev) => prev.filter((r) => r.id !== newRipple.id));
      }, 600);
    }

    // Handle success state
    if (success) {
      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), successDuration);
    }

    onClick?.(e);
  };

  const buttonContent = () => {
    if (loading) {
      return (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
          {loadingText || "Loading..."}
        </>
      );
    }

    if (isSuccess && successText) {
      return (
        <>
          <svg
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
          {successText}
        </>
      );
    }

    return children;
  };

  return (
    <Comp
      ref={buttonRef}
      data-slot="enhanced-button"
      className={cn(
        enhancedButtonVariants({
          variant,
          size,
          loading: loading || isSuccess,
          className,
        }),
      )}
      onClick={handleClick}
      disabled={loading || isSuccess}
      {...props}
    >
      {buttonContent()}
      {ripples.map((ripple) => (
        <Ripple key={ripple.id} x={ripple.x} y={ripple.y} />
      ))}
    </Comp>
  );
}

export { EnhancedButton, enhancedButtonVariants };
