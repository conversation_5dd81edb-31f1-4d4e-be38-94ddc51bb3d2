"use client";

import * as React from "react";
import { Search, X, Clock, TrendingUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { EnhancedInput } from "./enhanced-input";
import { EnhancedButton } from "./enhanced-button";
import { EnhancedCard, EnhancedCardContent } from "./enhanced-card";

interface SearchResult {
  id: string;
  title: string;
  description?: string;
  category?: string;
  url?: string;
  icon?: React.ReactNode;
}

interface SmartSearchProps {
  placeholder?: string;
  onSearch: (query: string) => Promise<SearchResult[]>;
  onSelect: (result: SearchResult) => void;
  className?: string;
  showRecentSearches?: boolean;
  showSuggestions?: boolean;
  maxResults?: number;
  debounceMs?: number;
}

function SmartSearch({
  placeholder = "Search...",
  onSearch,
  onSelect,
  className,
  showRecentSearches = true,
  showSuggestions = true,
  maxResults = 10,
  debounceMs = 300,
}: SmartSearchProps) {
  const [query, setQuery] = React.useState("");
  const [results, setResults] = React.useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = React.useState<string[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isOpen, setIsOpen] = React.useState(false);
  const [selectedIndex, setSelectedIndex] = React.useState(-1);

  const searchRef = React.useRef<HTMLDivElement>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);

  // Load recent searches from localStorage
  React.useEffect(() => {
    if (showRecentSearches) {
      const saved = localStorage.getItem("recent-searches");
      if (saved) {
        setRecentSearches(JSON.parse(saved));
      }
    }
  }, [showRecentSearches]);

  // Debounced search
  React.useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    const timer = setTimeout(async () => {
      try {
        const searchResults = await onSearch(query);
        setResults(searchResults.slice(0, maxResults));
      } catch (error) {
        console.error("Search error:", error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [query, onSearch, maxResults, debounceMs]);

  // Handle click outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const saveRecentSearch = (searchQuery: string) => {
    if (!showRecentSearches || !searchQuery.trim()) return;

    const updated = [
      searchQuery,
      ...recentSearches.filter((s) => s !== searchQuery),
    ].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem("recent-searches", JSON.stringify(updated));
  };

  const handleSelect = (result: SearchResult) => {
    saveRecentSearch(query);
    onSelect(result);
    setQuery("");
    setIsOpen(false);
    setSelectedIndex(-1);
  };

  const handleRecentSearchSelect = (searchQuery: string) => {
    setQuery(searchQuery);
    inputRef.current?.focus();
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem("recent-searches");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    const totalItems =
      results.length + (showRecentSearches ? recentSearches.length : 0);

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) => (prev + 1) % totalItems);
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) => (prev <= 0 ? totalItems - 1 : prev - 1));
        break;
      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0) {
          if (selectedIndex < results.length) {
            handleSelect(results[selectedIndex]);
          } else {
            const recentIndex = selectedIndex - results.length;
            handleRecentSearchSelect(recentSearches[recentIndex]);
          }
        }
        break;
      case "Escape":
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const showDropdown =
    isOpen &&
    (results.length > 0 ||
      (showRecentSearches && recentSearches.length > 0 && !query));

  return (
    <div ref={searchRef} className={cn("relative w-full max-w-md", className)}>
      <div className="relative">
        <EnhancedInput
          ref={inputRef}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          leftIcon={<Search className="h-4 w-4" />}
          rightIcon={
            query && (
              <button
                onClick={() => {
                  setQuery("");
                  setResults([]);
                  inputRef.current?.focus();
                }}
                className="hover:bg-accent rounded p-1 transition-colors"
              >
                <X className="h-3 w-3" />
              </button>
            )
          }
          loading={isLoading}
          className="pr-10"
        />
      </div>

      {showDropdown && (
        <EnhancedCard className="absolute top-full left-0 right-0 mt-1 z-50 max-h-96 overflow-auto">
          <EnhancedCardContent className="p-0">
            {/* Search Results */}
            {results.length > 0 && (
              <div className="py-2">
                {results.map((result, index) => (
                  <button
                    key={result.id}
                    onClick={() => handleSelect(result)}
                    className={cn(
                      "w-full px-4 py-2 text-left hover:bg-accent transition-colors flex items-center space-x-3",
                      selectedIndex === index && "bg-accent",
                    )}
                  >
                    {result.icon && (
                      <div className="flex-shrink-0">{result.icon}</div>
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{result.title}</div>
                      {result.description && (
                        <div className="text-sm text-muted-foreground truncate">
                          {result.description}
                        </div>
                      )}
                      {result.category && (
                        <div className="text-xs text-muted-foreground">
                          {result.category}
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            )}

            {/* Recent Searches */}
            {showRecentSearches && recentSearches.length > 0 && !query && (
              <div className="py-2 border-t">
                <div className="px-4 py-2 flex items-center justify-between">
                  <span className="text-sm font-medium text-muted-foreground">
                    Recent Searches
                  </span>
                  <EnhancedButton
                    variant="ghost"
                    size="sm"
                    onClick={clearRecentSearches}
                    className="h-auto p-1 text-xs"
                  >
                    Clear
                  </EnhancedButton>
                </div>
                {recentSearches.map((search, index) => (
                  <button
                    key={search}
                    onClick={() => handleRecentSearchSelect(search)}
                    className={cn(
                      "w-full px-4 py-2 text-left hover:bg-accent transition-colors flex items-center space-x-3",
                      selectedIndex === results.length + index && "bg-accent",
                    )}
                  >
                    <Clock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <span className="truncate">{search}</span>
                  </button>
                ))}
              </div>
            )}

            {/* No Results */}
            {query && results.length === 0 && !isLoading && (
              <div className="px-4 py-8 text-center text-muted-foreground">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No results found for "{query}"</p>
              </div>
            )}
          </EnhancedCardContent>
        </EnhancedCard>
      )}
    </div>
  );
}

export { SmartSearch, type SearchResult };
