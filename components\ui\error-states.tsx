"use client";

import * as React from "react";
import {
  <PERSON>ert<PERSON><PERSON>gle,
  RefreshCw,
  Home,
  ArrowLeft,
  Wifi,
  WifiOff,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { EnhancedButton } from "./enhanced-button";
import {
  EnhancedCard,
  EnhancedCard<PERSON>ontent,
  EnhancedCardHeader,
  EnhancedCardTitle,
} from "./enhanced-card";

interface ErrorStateProps {
  title?: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
  variant?: "error" | "warning" | "info" | "offline";
  icon?: React.ReactNode;
}

function ErrorState({
  title = "Something went wrong",
  description = "We encountered an error while processing your request.",
  action,
  secondaryAction,
  className,
  variant = "error",
  icon,
}: ErrorStateProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case "warning":
        return "border-warning/50 bg-warning/5";
      case "info":
        return "border-blue-500/50 bg-blue-500/5";
      case "offline":
        return "border-gray-500/50 bg-gray-500/5";
      default:
        return "border-destructive/50 bg-destructive/5";
    }
  };

  const getDefaultIcon = () => {
    switch (variant) {
      case "offline":
        return <WifiOff className="h-12 w-12 text-gray-500" />;
      default:
        return <AlertTriangle className="h-12 w-12 text-destructive" />;
    }
  };

  return (
    <EnhancedCard
      className={cn(
        "max-w-md mx-auto text-center",
        getVariantStyles(),
        className,
      )}
    >
      <EnhancedCardContent className="pt-6">
        <div className="flex flex-col items-center space-y-4">
          {icon || getDefaultIcon()}

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">{title}</h3>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>

          {(action || secondaryAction) && (
            <div className="flex flex-col sm:flex-row gap-2 w-full">
              {action && (
                <EnhancedButton
                  onClick={action.onClick}
                  className="flex-1"
                  variant="default"
                >
                  {action.label}
                </EnhancedButton>
              )}
              {secondaryAction && (
                <EnhancedButton
                  onClick={secondaryAction.onClick}
                  variant="outline"
                  className="flex-1"
                >
                  {secondaryAction.label}
                </EnhancedButton>
              )}
            </div>
          )}
        </div>
      </EnhancedCardContent>
    </EnhancedCard>
  );
}

interface EmptyStateProps {
  title?: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: React.ReactNode;
  className?: string;
}

function EmptyState({
  title = "No data found",
  description = "There's nothing to show here yet.",
  action,
  icon,
  className,
}: EmptyStateProps) {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center py-12 text-center",
        className,
      )}
    >
      <div className="space-y-4">
        {icon && <div className="flex justify-center">{icon}</div>}

        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-muted-foreground">
            {title}
          </h3>
          <p className="text-sm text-muted-foreground max-w-sm">
            {description}
          </p>
        </div>

        {action && (
          <EnhancedButton onClick={action.onClick} variant="outline">
            {action.label}
          </EnhancedButton>
        )}
      </div>
    </div>
  );
}

interface OfflineIndicatorProps {
  className?: string;
}

function OfflineIndicator({ className }: OfflineIndicatorProps) {
  const [isOnline, setIsOnline] = React.useState(true);

  React.useEffect(() => {
    const updateOnlineStatus = () => setIsOnline(navigator.onLine);

    window.addEventListener("online", updateOnlineStatus);
    window.addEventListener("offline", updateOnlineStatus);

    return () => {
      window.removeEventListener("online", updateOnlineStatus);
      window.removeEventListener("offline", updateOnlineStatus);
    };
  }, []);

  if (isOnline) return null;

  return (
    <div
      className={cn(
        "fixed top-0 left-0 right-0 z-50 bg-destructive text-destructive-foreground px-4 py-2 text-center text-sm",
        className,
      )}
    >
      <div className="flex items-center justify-center space-x-2">
        <WifiOff className="h-4 w-4" />
        <span>You're offline. Some features may not work.</span>
      </div>
    </div>
  );
}

interface RetryBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onRetry?: () => void;
  maxRetries?: number;
}

function RetryBoundary({
  children,
  fallback,
  onRetry,
  maxRetries = 3,
}: RetryBoundaryProps) {
  const [hasError, setHasError] = React.useState(false);
  const [retryCount, setRetryCount] = React.useState(0);

  const handleRetry = () => {
    if (retryCount < maxRetries) {
      setRetryCount((prev) => prev + 1);
      setHasError(false);
      onRetry?.();
    }
  };

  React.useEffect(() => {
    if (hasError) {
      // Auto-retry after a delay
      const timer = setTimeout(() => {
        if (retryCount < maxRetries) {
          handleRetry();
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [hasError, retryCount, maxRetries]);

  if (hasError) {
    return (
      fallback || (
        <ErrorState
          title="Something went wrong"
          description={`Failed to load content. ${retryCount < maxRetries ? "Retrying..." : "Please try again later."}`}
          action={
            retryCount < maxRetries
              ? undefined
              : {
                  label: "Retry",
                  onClick: handleRetry,
                }
          }
        />
      )
    );
  }

  return <>{children}</>;
}

export { ErrorState, EmptyState, OfflineIndicator, RetryBoundary };
