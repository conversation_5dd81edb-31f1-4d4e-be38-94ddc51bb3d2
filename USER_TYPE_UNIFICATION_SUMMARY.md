# User Type Unification Summary

## Changes Made

1. **Added DbUser type alias in `lib/types/database.ts`**:
   - Imported Supabase's User type as `SupabaseUser`
   - Created `DbUser` type alias for Supabase Auth User
   - This allows us to distinguish between Supabase's User and our domain User

2. **Created `lib/userMapper.ts`**:
   - Implemented `toDomainUser()` function to convert DbUser to domain User
   - Handles optional email field from Supabase by providing a default empty string
   - Maps timestamps with defaults for missing values

3. **Updated `lib/contexts/auth-context.tsx`**:
   - Changed import from `User` to use `DbUser` from database types
   - Updated AuthContextType interface to use `DbUser | null`
   - Updated component state to use `DbUser` type

4. **Updated AI Chat components**:
   - `components/ai-chat/financial-chat-interface.tsx`: Changed to accept `DbUser`
   - `components/ai-chat/receipt-processor.tsx`: Changed to accept `DbUser`
   - `components/ai-chat/lazy-ai-components.tsx`: Updated both lazy components to use `DbUser`

## Result

The User types are now properly unified:
- Supabase Auth User is imported as `DbUser` 
- Domain-level User type remains in `lib/types/database.ts`
- Clear separation between authentication user (DbUser) and domain user (User)
- Mapper function available for converting between types when needed

## No Breaking Changes

All components continue to work as before, but now have proper type safety distinguishing between Supabase's authentication User and our domain User model.
