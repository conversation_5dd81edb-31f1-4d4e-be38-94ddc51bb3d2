import React from "react";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AlertCircle, Info, CheckCircle } from "lucide-react";

interface AccessibleFieldProps {
  id: string;
  label: string;
  required?: boolean;
  error?: string;
  hint?: string;
  success?: string;
  className?: string;
  children: React.ReactNode;
}

export function AccessibleField({
  id,
  label,
  required = false,
  error,
  hint,
  success,
  className,
  children,
}: AccessibleFieldProps) {
  const hintId = hint ? `${id}-hint` : undefined;
  const errorId = error ? `${id}-error` : undefined;
  const successId = success ? `${id}-success` : undefined;

  const describedBy = [hintId, errorId, successId].filter(Boolean).join(" ");

  return (
    <div className={cn("space-y-2", className)}>
      <Label
        htmlFor={id}
        className={cn(
          "text-sm font-medium",
          error ? "text-red-700" : "text-gray-700",
        )}
      >
        {label}
        {required && (
          <span
            className="text-red-500 ml-1"
            aria-label="required"
            title="This field is required"
          >
            *
          </span>
        )}
      </Label>

      <div className="relative">
        {(() => {
          const child = children as React.ReactElement<any>;
          return React.cloneElement(child, {
            id,
            "aria-describedby": describedBy || undefined,
            "aria-invalid": error ? "true" : "false",
            "aria-required": required,
            className: cn(
              child.props?.className,
              error
                ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                : "",
              success
                ? "border-green-500 focus:border-green-500 focus:ring-green-500"
                : "",
            ),
          });
        })()}

        {/* Success icon */}
        {success && (
          <CheckCircle
            className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500"
            aria-hidden="true"
          />
        )}

        {/* Error icon */}
        {error && (
          <AlertCircle
            className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-red-500"
            aria-hidden="true"
          />
        )}
      </div>

      {/* Hint text */}
      {hint && (
        <p id={hintId} className="text-sm text-gray-600 flex items-start gap-1">
          <Info className="w-3 h-3 mt-0.5 flex-shrink-0" aria-hidden="true" />
          {hint}
        </p>
      )}

      {/* Error message */}
      {error && (
        <p
          id={errorId}
          className="text-sm text-red-600 flex items-start gap-1"
          role="alert"
          aria-live="polite"
        >
          <AlertCircle
            className="w-3 h-3 mt-0.5 flex-shrink-0"
            aria-hidden="true"
          />
          {error}
        </p>
      )}

      {/* Success message */}
      {success && (
        <p
          id={successId}
          className="text-sm text-green-600 flex items-start gap-1"
          role="status"
          aria-live="polite"
        >
          <CheckCircle
            className="w-3 h-3 mt-0.5 flex-shrink-0"
            aria-hidden="true"
          />
          {success}
        </p>
      )}
    </div>
  );
}

// Accessible input field
export function AccessibleInput({
  id,
  label,
  type = "text",
  placeholder,
  value,
  onChange,
  required = false,
  error,
  hint,
  success,
  className,
  ...props
}: {
  id: string;
  label: string;
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
  error?: string;
  hint?: string;
  success?: string;
  className?: string;
} & React.InputHTMLAttributes<HTMLInputElement>) {
  return (
    <AccessibleField
      id={id}
      label={label}
      required={required}
      error={error}
      hint={hint}
      success={success}
      className={className}
    >
      <Input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        {...props}
      />
    </AccessibleField>
  );
}

// Accessible textarea field
export function AccessibleTextarea({
  id,
  label,
  placeholder,
  value,
  onChange,
  required = false,
  error,
  hint,
  success,
  className,
  rows = 3,
  ...props
}: {
  id: string;
  label: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  required?: boolean;
  error?: string;
  hint?: string;
  success?: string;
  className?: string;
  rows?: number;
} & React.TextareaHTMLAttributes<HTMLTextAreaElement>) {
  return (
    <AccessibleField
      id={id}
      label={label}
      required={required}
      error={error}
      hint={hint}
      success={success}
      className={className}
    >
      <Textarea
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        rows={rows}
        {...props}
      />
    </AccessibleField>
  );
}

// Accessible select field
export function AccessibleSelect({
  id,
  label,
  value,
  onValueChange,
  placeholder = "Select an option",
  required = false,
  error,
  hint,
  success,
  className,
  children,
  ...props
}: {
  id: string;
  label: string;
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  hint?: string;
  success?: string;
  className?: string;
  children: React.ReactNode;
}) {
  return (
    <AccessibleField
      id={id}
      label={label}
      required={required}
      error={error}
      hint={hint}
      success={success}
      className={className}
    >
      <Select value={value} onValueChange={onValueChange} {...props}>
        <SelectTrigger>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>{children}</SelectContent>
      </Select>
    </AccessibleField>
  );
}

// Accessible checkbox field
export function AccessibleCheckbox({
  id,
  label,
  checked,
  onCheckedChange,
  required = false,
  error,
  hint,
  success,
  className,
  ...props
}: {
  id: string;
  label: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  required?: boolean;
  error?: string;
  hint?: string;
  success?: string;
  className?: string;
}) {
  return (
    <AccessibleField
      id={id}
      label={label}
      required={required}
      error={error}
      hint={hint}
      success={success}
      className={className}
    >
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id={id}
          checked={checked}
          onChange={(e) => onCheckedChange?.(e.target.checked)}
          className={cn(
            "w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2",
            error ? "border-red-500" : "",
          )}
          {...props}
        />
        <Label htmlFor={id} className="text-sm font-normal cursor-pointer">
          {label}
        </Label>
      </div>
    </AccessibleField>
  );
}
