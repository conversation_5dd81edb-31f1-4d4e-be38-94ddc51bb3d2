import { cn, formatCurrency } from "@/lib/utils";

describe("utils", () => {
  describe("cn (className utility)", () => {
    it("merges class names correctly", () => {
      const result = cn("px-4", "py-2", "bg-blue-500");
      expect(result).toContain("px-4");
      expect(result).toContain("py-2");
      expect(result).toContain("bg-blue-500");
    });

    it("handles conditional classes", () => {
      const isActive = true;
      const result = cn("base-class", isActive && "active-class");
      expect(result).toContain("base-class");
      expect(result).toContain("active-class");
    });

    it("handles undefined and null values", () => {
      const result = cn("base-class", undefined, null, "other-class");
      expect(result).toContain("base-class");
      expect(result).toContain("other-class");
    });
  });

  describe("formatCurrency", () => {
    it("formats positive numbers correctly", () => {
      expect(formatCurrency(1234.56)).toBe("$1,234.56");
    });

    it("formats negative numbers correctly", () => {
      expect(formatCurrency(-1234.56)).toBe("-$1,234.56");
    });

    it("formats zero correctly", () => {
      expect(formatCurrency(0)).toBe("$0.00");
    });

    it("handles large numbers", () => {
      expect(formatCurrency(1000000)).toBe("$1,000,000.00");
    });

    it("handles small decimal numbers", () => {
      expect(formatCurrency(0.99)).toBe("$0.99");
    });
  });
});
