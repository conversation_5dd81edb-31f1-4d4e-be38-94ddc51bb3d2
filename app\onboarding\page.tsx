"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/contexts/auth-context";
import { AuthGuard } from "@/lib/middleware/auth-guard";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { CheckCircle } from "lucide-react";

// Import onboarding steps - using simple versions for stability
import { ProfileSetup } from "@/components/onboarding/simple-profile-setup";

const ONBOARDING_STEPS = [
  {
    id: 1,
    title: "Setup Complete",
    description: "Welcome to your finance tracker!",
  },
];

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);
  const { user, profile, refreshProfile } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Check if user has already completed onboarding
    if (profile !== null) {
      if (
        profile.full_name &&
        profile.currency_code &&
        profile.monthly_income
      ) {
        // User has completed basic setup, redirect to dashboard
        router.replace("/dashboard");
        return;
      }
      // Profile loaded but onboarding not complete, show onboarding
      setIsCheckingOnboarding(false);
    }
  }, [profile, router]);

  const handleStepComplete = async (stepId: number) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps((prev) => [...prev, stepId]);
    }

    // Refresh profile data
    await refreshProfile();

    // Move to next step
    if (stepId < ONBOARDING_STEPS.length) {
      setCurrentStep(stepId + 1);
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkipToStep = (stepId: number) => {
    setCurrentStep(stepId);
  };

  const renderCurrentStep = () => {
    return (
      <ProfileSetup
        onComplete={() => router.push("/dashboard")}
        onSkip={() => router.push("/dashboard")}
      />
    );
  };

  const progressPercentage = (currentStep / ONBOARDING_STEPS.length) * 100;

  // Show loading state while checking onboarding status
  if (isCheckingOnboarding) {
    return (
      <AuthGuard requireAuth={true}>
        <div className="min-h-screen bg-gradient-to-br from-background to-muted flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Checking your setup...</p>
          </div>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard requireAuth={true}>
      <div className="min-h-screen bg-gradient-to-br from-background to-muted py-8">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Welcome to Personal Finance Tracker
            </h1>
            <p className="text-muted-foreground">
              Let's set up your account to get the most out of your financial
              tracking
            </p>
          </div>

          {/* Progress Bar */}
          <Card className="mb-8">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm font-medium text-foreground">
                  Step {currentStep} of {ONBOARDING_STEPS.length}
                </span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(progressPercentage)}% Complete
                </span>
              </div>
              <Progress value={progressPercentage} className="mb-4" />

              {/* Step indicators */}
              <div className="flex justify-between">
                {ONBOARDING_STEPS.map((step) => (
                  <div
                    key={step.id}
                    className={`flex flex-col items-center cursor-pointer ${
                      step.id <= currentStep ? "text-primary" : "text-muted-foreground"
                    }`}
                    onClick={() =>
                      step.id < currentStep && handleSkipToStep(step.id)
                    }
                  >
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center mb-2 ${
                        completedSteps.includes(step.id)
                          ? "bg-success text-success-foreground"
                          : step.id === currentStep
                            ? "bg-primary text-primary-foreground"
                            : "bg-muted text-muted-foreground"
                      }`}
                    >
                      {completedSteps.includes(step.id) ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        step.id
                      )}
                    </div>
                    <div className="text-center">
                      <div className="text-xs font-medium">{step.title}</div>
                      <div className="text-xs text-muted-foreground hidden sm:block">
                        {step.description}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Current Step Content */}
          <div className="mb-8">{renderCurrentStep()}</div>
        </div>
      </div>
    </AuthGuard>
  );
}
