import { NextResponse } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";
import { withSecurity, sanitizeInput } from "@/lib/utils/security-headers";

// Schema for extracted receipt data
const receiptSchema = z.object({
  merchant: z.string().describe("The name of the store or merchant"),
  amount: z.number().describe("The total amount of the purchase"),
  date: z.string().describe("The date of the purchase in YYYY-MM-DD format"),
  items: z
    .array(
      z.object({
        name: z.string().describe("The name of the item"),
        price: z.number().describe("The price of the item"),
        quantity: z.number().optional().describe("The quantity of the item"),
      }),
    )
    .describe("List of items purchased"),
  category: z
    .string()
    .describe(
      "The most appropriate spending category (Food & Dining, Shopping, Transportation, Entertainment, Healthcare, etc.)",
    ),
  confidence: z
    .number()
    .min(0)
    .max(1)
    .describe("Confidence level of the extraction (0-1)"),
});

const handleReceiptProcessing = async (request: Request) => {
  try {
    // Validate API key
    const apiKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    if (!apiKey) {
      return new Response(
        JSON.stringify({
          error: "OCR service is not configured. Please contact support.",
        }),
        {
          status: 503,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    const formData = await request.formData();
    const file = formData.get("receipt") as File;
    const userId = formData.get("userId") as string;

    // Validate inputs
    if (!file) {
      return new Response(JSON.stringify({ error: "No file provided" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    if (!userId || typeof userId !== "string" || userId.length < 10) {
      return new Response(JSON.stringify({ error: "Valid User ID required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/bmp",
      "image/webp",
      "application/pdf",
    ];
    if (!allowedTypes.includes(file.type)) {
      return new Response(
        JSON.stringify({
          error: "Invalid file type. Please upload an image or PDF.",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    // Validate file size (5MB limit for security)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return new Response(
        JSON.stringify({ error: "File too large. Maximum size is 5MB." }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    // Validate file name (prevent path traversal)
    const fileName = file.name;
    if (
      !fileName ||
      fileName.includes("..") ||
      fileName.includes("/") ||
      fileName.includes("\\")
    ) {
      return new Response(JSON.stringify({ error: "Invalid file name." }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Convert file to base64 for AI processing
    const bytes = await file.arrayBuffer();
    const base64 = Buffer.from(bytes).toString("base64");
    const mimeType = file.type;

    try {
      // Use Google's Gemini Vision model to extract receipt data
      const result = await generateObject({
        model: google("gemini-2.0-flash-exp"),
        schema: receiptSchema,
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `Please analyze this receipt image and extract the following information:
                
1. Merchant/Store name
2. Total amount spent
3. Date of purchase
4. List of items with prices
5. Most appropriate spending category
6. Your confidence level in the extraction

Please be as accurate as possible. If you can't read something clearly, indicate lower confidence.

Categories to choose from:
- Food & Dining
- Groceries
- Shopping
- Transportation
- Entertainment
- Healthcare
- Utilities
- Travel
- Education
- Personal Care
- Home & Garden
- Technology
- Clothing
- Other

Return the data in the specified JSON format.`,
              },
              {
                type: "image",
                image: `data:${mimeType};base64,${base64}`,
              },
            ],
          },
        ],
      });

      // Validate and clean the extracted data
      const extractedData = result.object;

      // Ensure date is in correct format
      if (
        extractedData.date &&
        !extractedData.date.match(/^\d{4}-\d{2}-\d{2}$/)
      ) {
        // Try to parse and reformat the date
        try {
          const parsedDate = new Date(extractedData.date);
          if (!isNaN(parsedDate.getTime())) {
            extractedData.date = parsedDate.toISOString().split("T")[0];
          } else {
            extractedData.date = new Date().toISOString().split("T")[0];
          }
        } catch {
          extractedData.date = new Date().toISOString().split("T")[0];
        }
      }

      // Ensure amount is positive
      if (extractedData.amount < 0) {
        extractedData.amount = Math.abs(extractedData.amount);
      }

      // Log successful extraction
      // Receipt processed successfully

      return NextResponse.json({
        success: true,
        extractedData,
        message: "Receipt processed successfully",
      });
    } catch (aiError) {
      // AI processing error occurred

      // Return error when AI processing fails
      return new Response(
        JSON.stringify({
          success: false,
          error: "Receipt processing failed - AI service unavailable",
          message:
            "Please try again later or enter transaction details manually",
        }),
        {
          status: 503,
          headers: { "Content-Type": "application/json" },
        },
      );
    }
  } catch (error) {
    // Receipt processing error occurred
    return NextResponse.json(
      { error: "Failed to process receipt. Please try again." },
      { status: 500 },
    );
  }
};

// Export the secured handler
export const POST = withSecurity(handleReceiptProcessing, {
  rateLimit: { limit: 20, windowMs: 15 * 60 * 1000 }, // 20 requests per 15 minutes
  corsOptions: {
    allowOrigins: ["http://localhost:3000", "https://localhost:3000"],
    allowMethods: ["POST", "OPTIONS"],
    allowHeaders: ["Content-Type"],
  },
});

// OPTIONS is handled by the security wrapper
