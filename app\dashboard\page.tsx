import { CashFlowChart } from "@/components/dashboard/cash-flow-chart";
import { SpendingCategoriesChart } from "@/components/dashboard/spending-categories-chart";
import { EnhancedSectionCards } from "@/components/enhanced-section-cards";
import { EnhancedDashboard } from "@/components/enhanced-dashboard";
import {
  FadeInView,
  StaggeredAnimation,
} from "@/components/ui/page-transitions";

export default function Page() {
  const breadcrumbItems = [
    { label: "Home", href: "/", tooltip: "Go to homepage" },
    { label: "Dashboard", tooltip: "Financial overview and insights" },
  ];

  return (
    <EnhancedDashboard
      title="Financial Dashboard"
      breadcrumbItems={breadcrumbItems}
      showSearch={true}
    >
      <div className="flex flex-col gap-6 py-6">
        <EnhancedSectionCards />

        <StaggeredAnimation
          staggerDelay={0.2}
          className="flex flex-col gap-6 px-4 lg:px-6"
        >
          <FadeInView>
            <CashFlowChart />
          </FadeInView>

          <FadeInView>
            <SpendingCategoriesChart />
          </FadeInView>
        </StaggeredAnimation>
      </div>
    </EnhancedDashboard>
  );
}
