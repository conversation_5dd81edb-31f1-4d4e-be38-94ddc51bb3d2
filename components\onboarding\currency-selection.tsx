"use client";

import { useState } from "react";
import { useAuth } from "@/lib/contexts/auth-context";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { DollarSign, Loader2, ArrowLeft } from "lucide-react";

interface CurrencySelectionProps {
  onComplete: () => void;
  onBack: () => void;
}

const CURRENCIES = [
  { code: "USD", name: "US Dollar", symbol: "$" },
  { code: "EUR", name: "Euro", symbol: "€" },
  { code: "GBP", name: "British Pound", symbol: "£" },
  { code: "JPY", name: "Japanese Yen", symbol: "¥" },
  { code: "CAD", name: "Canadian Dollar", symbol: "C$" },
  { code: "AUD", name: "Australian Dollar", symbol: "A$" },
  { code: "CHF", name: "Swiss Franc", symbol: "CHF" },
  { code: "CNY", name: "Chinese Yuan", symbol: "¥" },
  { code: "INR", name: "Indian Rupee", symbol: "₹" },
  { code: "KWD", name: "Kuwaiti Dinar", symbol: "د.ك" },
  { code: "BRL", name: "Brazilian Real", symbol: "R$" },
];

export function CurrencySelection({
  onComplete,
  onBack,
}: CurrencySelectionProps) {
  const { profile, updateProfile } = useAuth();
  const [formData, setFormData] = useState({
    currencyCode: profile?.currency_code || "USD",
    monthlyIncome: profile?.monthly_income?.toString() || "",
    salaryPaymentDate: profile?.salary_payment_date?.toString() || "1",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const selectedCurrency = CURRENCIES.find(
    (c) => c.code === formData.currencyCode,
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setError("");
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
    setError("");
  };

  const validateForm = () => {
    if (formData.monthlyIncome && isNaN(Number(formData.monthlyIncome))) {
      setError("Monthly income must be a valid number");
      return false;
    }

    if (formData.monthlyIncome && Number(formData.monthlyIncome) < 0) {
      setError("Monthly income cannot be negative");
      return false;
    }

    const paymentDate = Number(formData.salaryPaymentDate);
    if (paymentDate < 1 || paymentDate > 31) {
      setError("Payment date must be between 1 and 31");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError("");

    try {
      await updateProfile({
        currency_code: formData.currencyCode,
        monthly_income: formData.monthlyIncome
          ? Number(formData.monthlyIncome)
          : null,
        salary_payment_date: Number(formData.salaryPaymentDate),
      });

      onComplete();
    } catch (err) {
      console.error("Error updating financial preferences:", err);
      setError("Failed to update preferences. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="w-5 h-5" />
          Currency & Financial Preferences
        </CardTitle>
        <CardDescription>
          Set your base currency and income information for accurate tracking
        </CardDescription>
      </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Currency Selection */}
          <div className="space-y-2">
            <Label htmlFor="currency">Base Currency *</Label>
            <Select
              value={formData.currencyCode}
              onValueChange={(value) =>
                handleSelectChange("currencyCode", value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select your currency" />
              </SelectTrigger>
              <SelectContent>
                {CURRENCIES.map((currency) => (
                  <SelectItem key={currency.code} value={currency.code}>
                    <div className="flex items-center space-x-2">
                      <span className="font-mono text-sm">
                        {currency.symbol}
                      </span>
                      <span>
                        {currency.name} ({currency.code})
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500">
              This will be used for all financial calculations and displays
            </p>
          </div>

          {/* Currency Preview */}
          {selectedCurrency && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Preview</h4>
              <div className="text-2xl font-bold text-blue-700">
                {selectedCurrency.symbol}1,234.56
              </div>
              <p className="text-sm text-blue-600">
                This is how amounts will be displayed throughout the app
              </p>
            </div>
          )}

          {/* Monthly Income */}
          <div className="space-y-2">
            <Label htmlFor="monthlyIncome">Monthly Income (Optional)</Label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                {selectedCurrency?.symbol}
              </span>
              <Input
                id="monthlyIncome"
                name="monthlyIncome"
                type="number"
                value={formData.monthlyIncome}
                onChange={handleInputChange}
                placeholder="0.00"
                className="pl-8"
                min="0"
                step="0.01"
                disabled={loading}
              />
            </div>
            <p className="text-sm text-gray-500">
              This helps with budgeting and financial planning
            </p>
          </div>

          {/* Salary Payment Date */}
          <div className="space-y-2">
            <Label htmlFor="salaryPaymentDate">Salary Payment Date</Label>
            <Select
              value={formData.salaryPaymentDate}
              onValueChange={(value) =>
                handleSelectChange("salaryPaymentDate", value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select payment date" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                  <SelectItem key={day} value={day.toString()}>
                    {day}
                    {day === 1
                      ? "st"
                      : day === 2
                        ? "nd"
                        : day === 3
                          ? "rd"
                          : "th"}{" "}
                    of the month
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500">
              When do you typically receive your salary each month?
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              disabled={loading}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>

            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Continue"
              )}
            </Button>
          </div>
        </CardContent>
      </form>
    </Card>
  );
}
