/**
 * Centralized logging utility
 * Provides structured logging with different levels and proper production handling
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  level: LogLevel;
  message: string;
  data?: Record<string, unknown>;
  timestamp: string;
  context?: string;
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === "development";
  private isClient = typeof window !== "undefined";

  private formatMessage(
    level: LogLevel,
    message: string,
    data?: Record<string, unknown>,
    context?: string,
  ): LogEntry {
    return {
      level,
      message,
      data,
      timestamp: new Date().toISOString(),
      context,
    };
  }

  private shouldLog(level: LogLevel): boolean {
    // In production, only log warnings and errors
    if (!this.isDevelopment && level < LogLevel.WARN) {
      return false;
    }
    return true;
  }

  private logToConsole(entry: LogEntry): void {
    if (!this.shouldLog(entry.level)) return;

    const prefix = entry.context ? `[${entry.context}]` : "";
    const timestamp = this.isDevelopment ? entry.timestamp : "";

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(
          `🐛 ${timestamp} ${prefix} ${entry.message}`,
          entry.data || "",
        );
        break;
      case LogLevel.INFO:
        console.info(
          `ℹ️ ${timestamp} ${prefix} ${entry.message}`,
          entry.data || "",
        );
        break;
      case LogLevel.WARN:
        console.warn(
          `⚠️ ${timestamp} ${prefix} ${entry.message}`,
          entry.data || "",
        );
        break;
      case LogLevel.ERROR:
        console.error(
          `❌ ${timestamp} ${prefix} ${entry.message}`,
          entry.data || "",
        );
        break;
    }
  }

  private logToService(entry: LogEntry): void {
    // In production, send logs to external service
    if (process.env.NODE_ENV === "production" && entry.level >= LogLevel.WARN) {
      // Example: Send to Sentry, LogRocket, or other logging service
      // this.sendToExternalService(entry)
    }
  }

  debug(
    message: string,
    data?: Record<string, unknown>,
    context?: string,
  ): void {
    const entry = this.formatMessage(LogLevel.DEBUG, message, data, context);
    this.logToConsole(entry);
  }

  info(
    message: string,
    data?: Record<string, unknown>,
    context?: string,
  ): void {
    const entry = this.formatMessage(LogLevel.INFO, message, data, context);
    this.logToConsole(entry);
    this.logToService(entry);
  }

  warn(
    message: string,
    data?: Record<string, unknown> | unknown,
    context?: string,
  ): void {
    const safeData = this.normalizeErrorData(data);
    const entry = this.formatMessage(LogLevel.WARN, message, safeData, context);
    this.logToConsole(entry);
    this.logToService(entry);
  }

  error(
    message: string,
    data?: Record<string, unknown> | unknown,
    context?: string,
  ): void {
    // Safely convert unknown data to Record<string, unknown>
    const safeData = this.normalizeErrorData(data);
    const entry = this.formatMessage(LogLevel.ERROR, message, safeData, context);
    this.logToConsole(entry);
    this.logToService(entry);
  }

  private normalizeErrorData(data: Record<string, unknown> | unknown): Record<string, unknown> | undefined {
    if (data === null || data === undefined) {
      return undefined;
    }
    
    if (typeof data === 'object' && !Array.isArray(data)) {
      // For Error objects, extract useful properties
      if (data instanceof Error) {
        return {
          name: data.name,
          message: data.message,
          stack: data.stack,
        };
      }
      
      // For arrays or other objects, convert to a safe format
      if (Array.isArray(data)) {
        return { items: data.map((item, index) => ({ [index]: item })) };
      }
      
      // For plain objects, return as-is if it matches the expected type
      return data as Record<string, unknown>;
    }
    
    // For primitives, wrap in an object
    return { value: data };
  }

  // Specialized logging methods
  apiCall(method: string, url: string, data?: Record<string, unknown>): void {
    this.debug(`API ${method} ${url}`, data, "API");
  }

  apiResponse(
    method: string,
    url: string,
    status: number,
    data?: Record<string, unknown>,
  ): void {
    if (status >= 400) {
      this.error(
        `API ${method} ${url} failed with status ${status}`,
        data,
        "API",
      );
    } else {
      this.debug(
        `API ${method} ${url} succeeded with status ${status}`,
        data,
        "API",
      );
    }
  }

  userAction(action: string, data?: Record<string, unknown>): void {
    this.info(`User action: ${action}`, data, "USER");
  }

  performance(
    operation: string,
    duration: number,
    data?: Record<string, unknown>,
  ): void {
    if (duration > 1000) {
      this.warn(
        `Slow operation: ${operation} took ${duration}ms`,
        data,
        "PERF",
      );
    } else {
      this.debug(`Performance: ${operation} took ${duration}ms`, data, "PERF");
    }
  }

  errorBoundary(error: Error, errorInfo?: Record<string, unknown> | unknown): void {
    const safeErrorInfo = this.normalizeErrorData(errorInfo);
    this.error(
      "Error boundary caught error",
      {
        error: error.toString(),
        stack: error.stack,
        errorInfo: safeErrorInfo,
      },
      "ERROR_BOUNDARY",
    );
  }

  // Development-only logging
  dev(message: string, data?: Record<string, unknown>, context?: string): void {
    if (this.isDevelopment) {
      this.debug(message, data, context);
    }
  }
}

// Create singleton instance
export const logger = new Logger();

// Convenience exports
export const log = {
  debug: logger.debug.bind(logger),
  info: logger.info.bind(logger),
  warn: logger.warn.bind(logger),
  error: logger.error.bind(logger),
  dev: logger.dev.bind(logger),
  api: {
    call: logger.apiCall.bind(logger),
    response: logger.apiResponse.bind(logger),
  },
  user: logger.userAction.bind(logger),
  perf: logger.performance.bind(logger),
  errorBoundary: logger.errorBoundary.bind(logger),
};

// Legacy console.log replacement for gradual migration
export function devLog(...args: unknown[]): void {
  if (process.env.NODE_ENV === "development") {
    console.log(...args);
  }
}

// Error reporting utility
export function reportError(
  error: Error,
  context?: string,
  additionalData?: Record<string, unknown>,
): void {
  logger.error(
    `Unhandled error: ${error.message}`,
    {
      error: error.toString(),
      stack: error.stack,
      context,
      additionalData,
    },
    "ERROR_REPORT",
  );
}

export default logger;
