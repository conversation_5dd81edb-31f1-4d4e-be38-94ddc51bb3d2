import { Db<PERSON>ser, User } from "@/lib/types/database";

// Mapping function to convert DbUser to User
export function toDomainUser(u: DbUser): User {
    return {
        id: u.id,
        email: u.email || "", // Supabase User has optional email, but our domain User requires it
        created_at: u.created_at || new Date().toISOString(),
        updated_at: u.updated_at || new Date().toISOString(),
    };
}

