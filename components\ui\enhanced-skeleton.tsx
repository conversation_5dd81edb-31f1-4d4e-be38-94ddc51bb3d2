"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";

const skeletonVariants = cva("bg-accent rounded-md relative overflow-hidden", {
  variants: {
    variant: {
      default: "animate-pulse",
      shimmer:
        "animate-pulse before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/60 before:to-transparent",
      wave: "animate-[wave_1.5s_ease-in-out_infinite]",
    },
    size: {
      sm: "h-3",
      default: "h-4",
      lg: "h-6",
      xl: "h-8",
    },
  },
  defaultVariants: {
    variant: "shimmer",
    size: "default",
  },
});

interface EnhancedSkeletonProps
  extends React.ComponentProps<"div">,
    VariantProps<typeof skeletonVariants> {}

function EnhancedSkeleton({
  className,
  variant,
  size,
  ...props
}: EnhancedSkeletonProps) {
  return (
    <div
      data-slot="enhanced-skeleton"
      className={cn(skeletonVariants({ variant, size }), className)}
      {...props}
    />
  );
}

// Predefined skeleton layouts
function SkeletonCard() {
  return (
    <div className="space-y-4 p-6 border rounded-xl">
      <div className="flex items-center space-x-4">
        <EnhancedSkeleton className="h-12 w-12 rounded-full" />
        <div className="space-y-2 flex-1">
          <EnhancedSkeleton className="h-4 w-3/4" />
          <EnhancedSkeleton className="h-3 w-1/2" />
        </div>
      </div>
      <div className="space-y-2">
        <EnhancedSkeleton className="h-4 w-full" />
        <EnhancedSkeleton className="h-4 w-5/6" />
        <EnhancedSkeleton className="h-4 w-4/6" />
      </div>
    </div>
  );
}

function SkeletonTable({ rows = 5 }: { rows?: number }) {
  return (
    <div className="space-y-3">
      {/* Header */}
      <div className="flex space-x-4">
        <EnhancedSkeleton className="h-4 w-1/4" />
        <EnhancedSkeleton className="h-4 w-1/4" />
        <EnhancedSkeleton className="h-4 w-1/4" />
        <EnhancedSkeleton className="h-4 w-1/4" />
      </div>
      {/* Rows */}
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex space-x-4">
          <EnhancedSkeleton className="h-3 w-1/4" />
          <EnhancedSkeleton className="h-3 w-1/4" />
          <EnhancedSkeleton className="h-3 w-1/4" />
          <EnhancedSkeleton className="h-3 w-1/4" />
        </div>
      ))}
    </div>
  );
}

function SkeletonChart() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <EnhancedSkeleton className="h-6 w-1/3" />
        <EnhancedSkeleton className="h-4 w-20" />
      </div>
      <div className="h-64 flex items-end space-x-2">
        {Array.from({ length: 12 }).map((_, i) => (
          <EnhancedSkeleton
            key={i}
            className={`w-full`}
            style={{ height: `${Math.random() * 80 + 20}%` }}
          />
        ))}
      </div>
    </div>
  );
}

function SkeletonDashboard() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <EnhancedSkeleton className="h-8 w-1/3" />
        <EnhancedSkeleton className="h-10 w-32" />
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <SkeletonCard key={i} />
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SkeletonChart />
        <SkeletonChart />
      </div>
    </div>
  );
}

export {
  EnhancedSkeleton,
  SkeletonCard,
  SkeletonTable,
  SkeletonChart,
  SkeletonDashboard,
  skeletonVariants,
};
