"use client";

import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layouts/app-layout";
import { useAuth } from "@/lib/contexts/auth-context";
// Temporarily import components directly instead of lazy loading
import { FinancialChatInterface } from "@/components/ai-chat/financial-chat-interface";
import { ReceiptProcessor } from "@/components/ai-chat/receipt-processor";
// import {
//   LazyFinancialChat,
//   LazyReceiptProcessor,
//   LazyInsights,
//   preloadAIComponents
// } from '@/components/ai-chat/lazy-ai-components'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function AIChatPage() {
  const { user, profile } = useAuth();
  const [activeTab, setActiveTab] = useState("chat");

  // Temporarily disabled preloading
  // useEffect(() => {
  //   preloadAIComponents()
  // }, [])

  return (
    <AppLayout>
      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-semibold tracking-tight text-gray-900 dark:text-gray-100">
            AI Financial Assistant
          </h1>
          <p className="text-sm text-muted-foreground mt-1">
            Professional financial guidance powered by AI
          </p>
        </div>

        {/* Main Interface */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4"
        >
          <TabsList className="grid w-full grid-cols-3 h-10">
            <TabsTrigger value="chat" className="text-sm">
              Financial Chat
            </TabsTrigger>
            <TabsTrigger value="receipts" className="text-sm">
              Receipt Processing
            </TabsTrigger>
            <TabsTrigger value="insights" className="text-sm">
              Smart Insights
            </TabsTrigger>
          </TabsList>

          <TabsContent value="chat">
            <FinancialChatInterface user={user} profile={profile} />
          </TabsContent>

          <TabsContent value="receipts">
            <ReceiptProcessor user={user} profile={profile} />
          </TabsContent>

          <TabsContent value="insights">
            <div className="text-center p-8">
              <p className="text-muted-foreground">
                Smart Insights Coming Soon
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
