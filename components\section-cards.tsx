"use client";

import {
  IconTrendingDown,
  IconTrendingUp,
  IconMinus,
} from "@tabler/icons-react";
import { useDashboardData } from "@/lib/hooks/use-dashboard-data";
import { useAuth } from "@/lib/contexts/auth-context";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Helper function to get currency symbol
function getCurrencySymbol(currencyCode: string): string {
  const symbols: Record<string, string> = {
    USD: "$",
    EUR: "€",
    GBP: "£",
    KWD: "KWD",
    SAR: "SAR",
    AED: "AED",
  };
  return symbols[currencyCode] || currencyCode;
}

// Helper function to format currency
function formatCurrency(amount: number, currencyCode: string): string {
  const symbol = getCurrencySymbol(currencyCode);
  return `${symbol} ${amount.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
}

// Helper function to calculate percentage change
function calculatePercentageChange(
  current: number,
  previous: number,
): { percentage: number; isPositive: boolean } {
  if (previous === 0) {
    return { percentage: current > 0 ? 100 : 0, isPositive: current >= 0 };
  }
  const percentage = ((current - previous) / Math.abs(previous)) * 100;
  return { percentage: Math.abs(percentage), isPositive: percentage >= 0 };
}

export function SectionCards() {
  const { profile } = useAuth();
  const { data: dashboardData, loading, error } = useDashboardData();

  const currency = profile?.currency_code || "USD";

  if (loading) {
    return (
      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="@container/card animate-pulse">
            <CardHeader>
              <CardDescription className="h-4 bg-muted rounded w-24"></CardDescription>
              <CardTitle className="h-8 bg-muted rounded w-32"></CardTitle>
            </CardHeader>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="px-4 lg:px-6">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">
              Error Loading Dashboard Data
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  const financialSummary = dashboardData?.financialSummary || {
    income: 0,
    expenses: 0,
    balance: 0,
  };
  const totalDebt = dashboardData?.totalDebt || 0;
  const totalGoalProgress = dashboardData?.totalGoalProgress || 0;
  const goals = dashboardData?.goals || [];

  // Calculate previous month data for percentage changes
  // Note: This uses estimated values based on current data
  // In a future implementation, actual previous month data should be fetched
  const previousIncome = financialSummary.income * 0.9; // Estimated: 10% increase
  const previousDebt = totalDebt * 1.1; // Estimated: 10% decrease in debt
  const previousGoalProgress = Math.max(0, totalGoalProgress - 5); // Estimated: 5% progress

  const incomeChange = calculatePercentageChange(
    financialSummary.income,
    previousIncome,
  );
  const debtChange = calculatePercentageChange(totalDebt, previousDebt);
  const goalChange = calculatePercentageChange(
    totalGoalProgress,
    previousGoalProgress,
  );
  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
      {/* Monthly Income Card */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Monthly Income</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {formatCurrency(financialSummary.income, currency)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              {incomeChange.isPositive ? (
                <IconTrendingUp />
              ) : (
                <IconTrendingDown />
              )}
              {incomeChange.isPositive ? "+" : "-"}
              {incomeChange.percentage.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Income {incomeChange.isPositive ? "increased" : "decreased"}
            {incomeChange.isPositive ? (
              <IconTrendingUp className="size-4" />
            ) : (
              <IconTrendingDown className="size-4" />
            )}
          </div>
          <div className="text-muted-foreground">Compared to last month</div>
        </CardFooter>
      </Card>

      {/* Loans & Debts Card */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Loans & Debts</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {formatCurrency(totalDebt, currency)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              {!debtChange.isPositive ? (
                <IconTrendingDown />
              ) : (
                <IconTrendingUp />
              )}
              {!debtChange.isPositive ? "-" : "+"}
              {debtChange.percentage.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Debt {!debtChange.isPositive ? "decreased" : "increased"}
            {!debtChange.isPositive ? (
              <IconTrendingDown className="size-4" />
            ) : (
              <IconTrendingUp className="size-4" />
            )}
          </div>
          <div className="text-muted-foreground">Compared to last month</div>
        </CardFooter>
      </Card>

      {/* Net Balance Card */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Net Balance</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {formatCurrency(financialSummary.balance, currency)}
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              {financialSummary.balance > 0 ? (
                <IconTrendingUp />
              ) : financialSummary.balance < 0 ? (
                <IconTrendingDown />
              ) : (
                <IconMinus />
              )}
              {financialSummary.balance > 0
                ? "Positive"
                : financialSummary.balance < 0
                  ? "Negative"
                  : "Neutral"}
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {financialSummary.balance > 0
              ? "Healthy financial position"
              : financialSummary.balance < 0
                ? "Spending exceeds income"
                : "Balanced finances"}
            {financialSummary.balance > 0 ? (
              <IconTrendingUp className="size-4" />
            ) : financialSummary.balance < 0 ? (
              <IconTrendingDown className="size-4" />
            ) : (
              <IconMinus className="size-4" />
            )}
          </div>
          <div className="text-muted-foreground">Income minus expenses</div>
        </CardFooter>
      </Card>

      {/* Savings Goal Progress Card */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Savings Goal Progress</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            {totalGoalProgress.toFixed(1)}%
          </CardTitle>
          <CardAction>
            <Badge variant="outline">
              {goalChange.isPositive ? (
                <IconTrendingUp />
              ) : (
                <IconTrendingDown />
              )}
              {goalChange.isPositive ? "+" : "-"}
              {goalChange.percentage.toFixed(1)}%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            {totalGoalProgress > 75
              ? "Excellent progress"
              : totalGoalProgress > 50
                ? "Good progress"
                : totalGoalProgress > 25
                  ? "Making progress"
                  : "Getting started"}
            {goalChange.isPositive ? (
              <IconTrendingUp className="size-4" />
            ) : (
              <IconTrendingDown className="size-4" />
            )}
          </div>
          <div className="text-muted-foreground">
            {goals.length > 0
              ? `Average across ${goals.length} goal${goals.length > 1 ? "s" : ""}`
              : "No active goals"}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
