import { cn } from "@/lib/utils";
import { CheckCircle, Clock, Pause, XCircle, AlertCircle } from "lucide-react";

export interface StatusIndicatorProps {
  status: "active" | "completed" | "paused" | "cancelled" | "pending" | "error";
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
  showText?: boolean;
  className?: string;
  "aria-label"?: string;
}

const statusConfig = {
  active: {
    color: "text-green-600 bg-green-100",
    icon: CheckCircle,
    text: "Active",
    description: "Currently active and running",
  },
  completed: {
    color: "text-blue-600 bg-blue-100",
    icon: CheckCircle,
    text: "Completed",
    description: "Successfully completed",
  },
  paused: {
    color: "text-yellow-600 bg-yellow-100",
    icon: Pause,
    text: "Paused",
    description: "Temporarily paused",
  },
  cancelled: {
    color: "text-red-600 bg-red-100",
    icon: XCircle,
    text: "Cancelled",
    description: "Cancelled or stopped",
  },
  pending: {
    color: "text-gray-600 bg-gray-100",
    icon: Clock,
    text: "Pending",
    description: "Waiting to start",
  },
  error: {
    color: "text-red-600 bg-red-100",
    icon: AlertCircle,
    text: "Error",
    description: "Error occurred",
  },
};

const sizeConfig = {
  sm: {
    container: "px-2 py-1 text-xs",
    icon: "w-3 h-3",
    gap: "gap-1",
  },
  md: {
    container: "px-3 py-1.5 text-sm",
    icon: "w-4 h-4",
    gap: "gap-2",
  },
  lg: {
    container: "px-4 py-2 text-base",
    icon: "w-5 h-5",
    gap: "gap-2",
  },
};

export function StatusIndicator({
  status,
  size = "md",
  showIcon = true,
  showText = true,
  className,
  "aria-label": ariaLabel,
  ...props
}: StatusIndicatorProps) {
  const config = statusConfig[status];
  const sizeStyles = sizeConfig[size];
  const Icon = config.icon;

  const accessibleLabel =
    ariaLabel || `Status: ${config.text}. ${config.description}`;

  return (
    <div
      className={cn(
        "inline-flex items-center rounded-full font-medium",
        config.color,
        sizeStyles.container,
        sizeStyles.gap,
        className,
      )}
      role="status"
      aria-label={accessibleLabel}
      title={config.description}
      {...props}
    >
      {showIcon && <Icon className={sizeStyles.icon} aria-hidden="true" />}
      {showText && <span>{config.text}</span>}
      {/* Screen reader only text for additional context */}
      <span className="sr-only">{config.description}</span>
    </div>
  );
}

// Goal-specific status indicator
export function GoalStatusIndicator({
  status,
  progress,
  size = "md",
  className,
}: {
  status: "active" | "completed" | "paused";
  progress?: number;
  size?: "sm" | "md" | "lg";
  className?: string;
}) {
  const progressText =
    progress !== undefined ? ` (${Math.round(progress)}% complete)` : "";
  const ariaLabel = `Goal status: ${status}${progressText}`;

  return (
    <StatusIndicator
      status={status}
      size={size}
      className={className}
      aria-label={ariaLabel}
    />
  );
}

// Transaction type indicator
export function TransactionTypeIndicator({
  type,
  amount,
  size = "md",
  className,
}: {
  type: "income" | "expense";
  amount?: number;
  size?: "sm" | "md" | "lg";
  className?: string;
}) {
  const status = type === "income" ? "active" : "error";
  const amountText =
    amount !== undefined ? ` of $${Math.abs(amount).toFixed(2)}` : "";
  const ariaLabel = `Transaction type: ${type}${amountText}`;

  return (
    <div
      className={cn(
        "inline-flex items-center rounded-full font-medium",
        type === "income"
          ? "text-green-600 bg-green-100"
          : "text-red-600 bg-red-100",
        sizeConfig[size].container,
        sizeConfig[size].gap,
        className,
      )}
      role="status"
      aria-label={ariaLabel}
    >
      <span
        className={cn(
          "w-2 h-2 rounded-full",
          type === "income" ? "bg-green-600" : "bg-red-600",
        )}
        aria-hidden="true"
      />
      <span className="capitalize">{type}</span>
      <span className="sr-only">
        {type === "income" ? "Money received" : "Money spent"}
      </span>
    </div>
  );
}

// Priority indicator
export function PriorityIndicator({
  priority,
  size = "md",
  className,
}: {
  priority: "low" | "medium" | "high" | "urgent";
  size?: "sm" | "md" | "lg";
  className?: string;
}) {
  const priorityConfig = {
    low: { status: "pending" as const, text: "Low Priority" },
    medium: { status: "active" as const, text: "Medium Priority" },
    high: { status: "error" as const, text: "High Priority" },
    urgent: { status: "error" as const, text: "Urgent Priority" },
  };

  const config = priorityConfig[priority];
  const ariaLabel = `Priority level: ${config.text}`;

  return (
    <StatusIndicator
      status={config.status}
      size={size}
      className={className}
      aria-label={ariaLabel}
      showIcon={false}
      showText={true}
    />
  );
}

// Progress indicator with accessibility
export function ProgressIndicator({
  value,
  max = 100,
  size = "md",
  className,
  label,
}: {
  value: number;
  max?: number;
  size?: "sm" | "md" | "lg";
  className?: string;
  label?: string;
}) {
  const percentage = Math.round((value / max) * 100);
  const ariaLabel = label
    ? `${label}: ${percentage}% complete`
    : `Progress: ${percentage}% complete`;

  return (
    <div
      className={cn("flex items-center", sizeConfig[size].gap, className)}
      role="progressbar"
      aria-valuenow={value}
      aria-valuemin={0}
      aria-valuemax={max}
      aria-label={ariaLabel}
    >
      <div
        className={cn(
          "flex-1 bg-gray-200 rounded-full overflow-hidden",
          size === "sm" ? "h-2" : size === "md" ? "h-3" : "h-4",
        )}
      >
        <div
          className={cn(
            "h-full transition-all duration-300",
            percentage < 30
              ? "bg-red-500"
              : percentage < 70
                ? "bg-yellow-500"
                : "bg-green-500",
          )}
          style={{ width: `${percentage}%` }}
          aria-hidden="true"
        />
      </div>
      <span
        className={cn(
          "font-medium tabular-nums",
          sizeConfig[size].container.includes("text-xs")
            ? "text-xs"
            : sizeConfig[size].container.includes("text-sm")
              ? "text-sm"
              : "text-base",
        )}
      >
        {percentage}%
      </span>
    </div>
  );
}
