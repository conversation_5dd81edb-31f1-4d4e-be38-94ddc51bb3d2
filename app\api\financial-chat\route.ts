import { google } from "@ai-sdk/google";
import { createDataStreamResponse, streamText, tool } from "ai";
import { z } from "zod";
import {
  getUserTransactions,
  getUserGoals,
  getUserRecurringPayments,
  getUserLoans,
} from "@/lib/supabase/queries";
import { withSecurity, sanitizeInput } from "@/lib/utils/security-headers";
import { log } from "@/lib/utils/logger";
import type { ChatMessage } from "@/lib/types/database";

// Define request type
interface FinancialChatRequest {
  messages: ChatMessage[];
  financialContext?: Record<string, unknown>;
  userId: string;
}

const handleFinancialChat = async (req: Request) => {
  try {
    // Validate API key
    const apiKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    if (!apiKey) {
      return new Response(
        JSON.stringify({
          error: "AI service is not configured. Please contact support.",
        }),
        {
          status: 503,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    const requestData = await req.json();

    // Validate request data structure
    if (!requestData || typeof requestData !== 'object') {
      return new Response(
        JSON.stringify({ error: "Invalid request data" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    const sanitizedData = sanitizeInput(requestData);

    // Type-safe extraction with validation
    const messages = Array.isArray((sanitizedData as any)?.messages)
      ? (sanitizedData as any).messages as ChatMessage[]
      : [];
    const financialContext = (sanitizedData as any)?.financialContext as Record<string, unknown> | undefined;
    const userId = typeof (sanitizedData as any)?.userId === 'string'
      ? (sanitizedData as any).userId as string
      : '';

    // Validate required fields
    if (!userId || messages.length === 0) {
      return new Response(
        JSON.stringify({ error: "Missing required fields: userId and messages" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Validate request data
    if (!messages || !Array.isArray(messages)) {
      return new Response(
        JSON.stringify({
          error: "Invalid request: messages array is required",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    // Validate userId
    if (!userId || typeof userId !== "string") {
      return new Response(
        JSON.stringify({ error: "Invalid request: userId is required" }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    // Additional message validation and sanitization
    const sanitizedMessages = messages.map((msg) => ({
      ...msg,
      content:
        typeof msg.content === "string" ? msg.content.slice(0, 10000) : "", // Limit message length
      role: ["user", "assistant", "system"].includes(msg.role)
        ? msg.role
        : "user", // Validate role
    }));

    // Basic rate limiting check (in production, use Redis or similar)
    const messageCount = sanitizedMessages.length;
    if (messageCount > 50) {
      return new Response(
        JSON.stringify({
          error: "Too many messages in conversation. Please start a new chat.",
        }),
        {
          status: 429,
          headers: { "Content-Type": "application/json" },
        },
      );
    }
    let stepCounter = 0;

    // Get user's financial data for context
    let financialData = "";
    if (userId) {
      try {
        const [transactions, goals, recurringPayments, loans] =
          await Promise.all([
            getUserTransactions(userId).catch(() => []),
            getUserGoals(userId).catch(() => []),
            getUserRecurringPayments(userId).catch(() => []),
            getUserLoans(userId).catch(() => []),
          ]);

        // Create financial summary
        const totalIncome = transactions
          .filter((t) => t.amount > 0)
          .reduce((sum, t) => sum + t.amount, 0);

        const totalExpenses = transactions
          .filter((t) => t.amount < 0)
          .reduce((sum, t) => sum + Math.abs(t.amount), 0);

        const totalGoals = goals.reduce((sum, g) => sum + g.target_amount, 0);
        const totalGoalProgress = goals.reduce(
          (sum, g) => sum + g.current_amount,
          0,
        );

        const totalDebt = loans.reduce((sum, l) => sum + l.current_balance, 0);
        const monthlyRecurring = recurringPayments
          .filter((p) => p.is_active)
          .reduce((sum, p) => sum + (p.monthly_payment || 0), 0);

        financialData = `
Recent Financial Data:
- Total Income (recent): $${totalIncome.toFixed(2)}
- Total Expenses (recent): $${totalExpenses.toFixed(2)}
- Net Cash Flow: $${(totalIncome - totalExpenses).toFixed(2)}
- Active Goals: ${goals.length} (Total target: $${totalGoals.toFixed(2)}, Progress: $${totalGoalProgress.toFixed(2)})
- Outstanding Debt: $${totalDebt.toFixed(2)}
- Monthly Recurring Payments: $${monthlyRecurring.toFixed(2)}
- Recent Transactions: ${transactions
          .slice(0, 5)
          .map((t) => `${t.description}: $${t.amount}`)
          .join(", ")}
        `.trim();
      } catch (error) {
        log.error("Error fetching financial data", error, "FINANCIAL_CHAT_API");
      }
    }

    const systemMessage = `${financialContext}

${financialData}

You are a knowledgeable and supportive personal financial advisor AI. Your role is to:

1. Provide personalized financial advice based on the user's profile and financial data
2. Help with budgeting, saving, investing, and debt management
3. Offer practical, actionable recommendations
4. Be encouraging and supportive while being realistic
5. Use the user's actual financial data when available to give specific advice
6. Suggest concrete steps and strategies
7. Help set realistic financial goals and timelines

Guidelines:
- Always be encouraging and positive
- Provide specific, actionable advice
- Use real numbers from their financial data when relevant
- Suggest practical next steps
- Be mindful of their income level and financial situation
- Offer both short-term and long-term strategies
- Explain financial concepts in simple terms
- Focus on building healthy financial habits

If you don't have enough information about their specific situation, ask clarifying questions to provide better advice.`;

    return createDataStreamResponse({
      execute: async (dataStream) => {
        const result = streamText({
          model: google("gemini-2.0-flash-exp"),
          system: systemMessage,
          messages,
          toolCallStreaming: true,
          tools: {
            GetFinancialSummary: tool({
              description:
                "Get a summary of the user's current financial situation",
              parameters: z.object({
                aspect: z
                  .string()
                  .describe(
                    "The financial aspect to summarize (income, expenses, goals, debt, etc.)",
                  ),
              }),
              execute: async ({ aspect }) => {
                return `Financial summary for ${aspect}: ${financialData}`;
              },
            }),
            CalculateBudget: tool({
              description:
                "Calculate budget recommendations based on income and expenses",
              parameters: z.object({
                income: z.number().describe("Monthly income"),
                expenses: z.number().describe("Monthly expenses"),
              }),
              execute: async ({ income, expenses }) => {
                const surplus = income - expenses;
                const emergencyFund = income * 3; // 3 months of income
                const savingsRate = surplus > 0 ? (surplus / income) * 100 : 0;

                return `Budget Analysis:
- Monthly Surplus/Deficit: $${surplus.toFixed(2)}
- Recommended Emergency Fund: $${emergencyFund.toFixed(2)}
- Current Savings Rate: ${savingsRate.toFixed(1)}%
- Recommended Savings Rate: 20%
- Recommended Budget Allocation:
  * Housing: 25-30% of income
  * Transportation: 10-15% of income
  * Food: 10-15% of income
  * Savings: 20% of income
  * Entertainment: 5-10% of income`;
              },
            }),
            DebtPayoffStrategy: tool({
              description: "Calculate debt payoff strategies",
              parameters: z.object({
                totalDebt: z.number().describe("Total debt amount"),
                monthlyPayment: z
                  .number()
                  .describe("Current monthly payment capacity"),
              }),
              execute: async ({ totalDebt, monthlyPayment }) => {
                const snowballMonths = Math.ceil(totalDebt / monthlyPayment);
                const avalancheMonths = Math.ceil(
                  totalDebt / (monthlyPayment * 1.1),
                ); // Assuming 10% interest savings

                return `Debt Payoff Strategies:
- Debt Snowball: Pay minimums on all debts, extra on smallest balance first
  * Estimated payoff time: ${snowballMonths} months
  * Psychological benefit: Quick wins boost motivation
  
- Debt Avalanche: Pay minimums on all debts, extra on highest interest rate first
  * Estimated payoff time: ${avalancheMonths} months
  * Financial benefit: Saves more money on interest
  
- Recommended extra payment: Increase monthly payment by 20% if possible
- Consider debt consolidation if you have multiple high-interest debts`;
              },
            }),
          },
          maxSteps: 3,
          onStepFinish: ({
            toolCalls,
            toolResults,
            finishReason,
            usage,
            text,
          }) => {
            stepCounter++;
            log.dev(
              `Financial Chat Step ${stepCounter} Finished`,
              {
                finishReason,
                text: text?.substring(0, 100) + "...",
                toolCallsCount: toolCalls?.length || 0,
                toolResultsCount: toolResults?.length || 0,
                usage,
              },
              "FINANCIAL_CHAT_API",
            );

            if (toolCalls && toolCalls.length > 0) {
              log.dev(
                "Tool Calls",
                toolCalls.reduce((acc, call, index) => {
                  acc[`tool_${index}`] = {
                    tool: call.toolName,
                    args: call.args,
                  };
                  return acc;
                }, {} as Record<string, unknown>),
                "FINANCIAL_CHAT_API",
              );
            }

            if (toolResults && toolResults.length > 0) {
              log.dev(
                "Tool Results",
                toolResults.reduce((acc, result, index) => {
                  acc[`result_${index}`] = {
                    index: index + 1,
                    result:
                      typeof result === "object"
                        ? JSON.stringify(result).substring(0, 200) + "..."
                        : result,
                  };
                  return acc;
                }, {} as Record<string, unknown>),
                "FINANCIAL_CHAT_API",
              );
            }
          },
        });

        result.mergeIntoDataStream(dataStream);
      },
    });
  } catch (error) {
    log.error("Error in financial chat API", error, "FINANCIAL_CHAT_API");
    return new Response(
      JSON.stringify({ error: "Failed to process financial chat request" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      },
    );
  }
};

// Export the secured handler
export const POST = withSecurity(handleFinancialChat, {
  rateLimit: { limit: 50, windowMs: 15 * 60 * 1000 }, // 50 requests per 15 minutes
  corsOptions: {
    allowOrigins: ["http://localhost:3000", "https://localhost:3000"],
    allowMethods: ["POST", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization"],
  },
});
