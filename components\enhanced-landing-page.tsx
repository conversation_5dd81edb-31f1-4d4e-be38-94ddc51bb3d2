"use client";

import * as React from "react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/contexts/auth-context";
import { EnhancedButton } from "@/components/ui/enhanced-button";
import {
  EnhancedCard,
  EnhancedCardContent,
  EnhancedCardDescription,
  EnhancedCardHeader,
  EnhancedCardTitle,
} from "@/components/ui/enhanced-card";
import {
  PageTransition,
  FadeInView,
  StaggeredAnimation,
  SlideInView,
} from "@/components/ui/page-transitions";
import {
  HoverScale,
  Floating,
  Counter,
  Typewriter,
} from "@/components/ui/micro-interactions";
import { EnhancedTooltip } from "@/components/ui/enhanced-tooltip";
import {
  TouchTarget,
  ReducedMotion,
} from "@/components/ui/accessibility-helpers";
import { BrandedSpinner } from "@/components/ui/loading-states";
import {
  <PERSON><PERSON>,
  TrendingUp,
  <PERSON><PERSON><PERSON>,
  Target,
  Shield,
  Zap,
  <PERSON>,
  Star,
} from "lucide-react";

const features = [
  {
    icon: <TrendingUp className="h-8 w-8 text-green-600" />,
    title: "Smart Analytics",
    description:
      "Get AI-powered insights into your spending patterns and financial health with real-time analysis.",
    benefits: [
      "Predictive spending alerts",
      "Trend analysis",
      "Custom reports",
    ],
  },
  {
    icon: <PieChart className="h-8 w-8 text-purple-600" />,
    title: "Automatic Categorization",
    description:
      "Upload receipts and let AI automatically categorize and track your expenses with 99% accuracy.",
    benefits: [
      "OCR receipt scanning",
      "Smart categorization",
      "Expense tracking",
    ],
  },
  {
    icon: <Target className="h-8 w-8 text-orange-600" />,
    title: "Goal Tracking",
    description:
      "Set financial goals and track your progress with visual indicators and AI-powered guidance.",
    benefits: [
      "Visual progress tracking",
      "Milestone celebrations",
      "Smart recommendations",
    ],
  },
  {
    icon: <Shield className="h-8 w-8 text-blue-600" />,
    title: "Bank-Level Security",
    description:
      "Your data is protected with enterprise-grade encryption and security measures.",
    benefits: [
      "256-bit encryption",
      "Secure authentication",
      "Privacy protection",
    ],
  },
  {
    icon: <Zap className="h-8 w-8 text-yellow-600" />,
    title: "Lightning Fast",
    description:
      "Experience blazing-fast performance with optimized loading and smooth interactions.",
    benefits: ["Instant sync", "Offline support", "Real-time updates"],
  },
  {
    icon: <Users className="h-8 w-8 text-indigo-600" />,
    title: "Personal Assistant",
    description:
      "Get personalized financial advice and recommendations from our AI assistant.",
    benefits: ["24/7 AI support", "Custom advice", "Learning algorithms"],
  },
];

const stats = [
  { label: "Active Users", value: 50000, suffix: "+" },
  { label: "Transactions Tracked", value: 2500000, suffix: "+" },
  { label: "Money Saved", value: ********, prefix: "$", suffix: "+" },
  { label: "User Satisfaction", value: 98, suffix: "%" },
];

export function EnhancedLandingPage() {
  const { user, loading, isOnboardingComplete } = useAuth();
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);
  // Component visibility state

  useEffect(() => {
    setIsVisible(true);

    if (!loading && user) {
      // If user is authenticated, redirect based on onboarding status
      if (isOnboardingComplete) {
        router.push("/dashboard");
      } else {
        router.push("/onboarding");
      }
    }
  }, [user, loading, router, isOnboardingComplete]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <BrandedSpinner size="lg" message="Loading your financial future..." />
      </div>
    );
  }

  if (user) {
    return null; // Will redirect to dashboard
  }

  return (
    <ReducedMotion
      fallback={
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
          <div className="container mx-auto px-4 py-16">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center mb-6">
                <Wallet className="h-12 w-12 text-blue-600 mr-3" />
                <h1 className="text-4xl font-bold text-gray-900">
                  Personal Finance Tracker
                </h1>
              </div>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Take control of your finances with AI-powered insights, smart
                categorization, and comprehensive tracking.
              </p>
            </div>
          </div>
        </div>
      }
    >
      <PageTransition variant="fade">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
          {/* Hero Section */}
          <section className="relative">
            <div className="container mx-auto px-4 py-20">
              <div className="text-center mb-16">
                <SlideInView direction="down">
                  <div className="flex items-center justify-center mb-6">
                    <Floating>
                      <Wallet className="h-16 w-16 text-blue-600 mr-4" />
                    </Floating>
                    <div>
                      <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-2">
                        Personal Finance
                      </h1>
                      <Typewriter
                        text="Tracker"
                        speed={100}
                        className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                      />
                    </div>
                  </div>
                </SlideInView>

                <FadeInView>
                  <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto mb-8">
                    Take control of your finances with AI-powered insights,
                    smart categorization, and comprehensive tracking. Your
                    financial future starts here.
                  </p>
                </FadeInView>

                <SlideInView direction="up">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <button
                      onClick={() => router.push("/auth/signup")}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg text-lg min-w-[200px] transition-colors duration-200"
                    >
                      Start Your Journey
                    </button>

                    <button
                      onClick={() => router.push("/auth/signin")}
                      className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-semibold py-3 px-8 rounded-lg text-lg min-w-[200px] transition-colors duration-200"
                    >
                      Sign In
                    </button>
                  </div>
                </SlideInView>
              </div>

              {/* Stats Section */}
              <FadeInView>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20">
                  {stats.map((stat, index) => (
                    <div key={stat.label} className="text-center">
                      <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                        <Counter
                          from={0}
                          to={stat.value}
                          duration={2 + index * 0.5}
                          prefix={stat.prefix}
                          suffix={stat.suffix}
                        />
                      </div>
                      <p className="text-gray-600">{stat.label}</p>
                    </div>
                  ))}
                </div>
              </FadeInView>
            </div>
          </section>

          {/* Features Section */}
          <section className="py-20 bg-white/50 backdrop-blur-sm">
            <div className="container mx-auto px-4">
              <FadeInView>
                <div className="text-center mb-16">
                  <h2 className="text-4xl font-bold text-gray-900 mb-4">
                    Everything you need to manage your finances
                  </h2>
                  <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                    Powerful features designed to make financial management
                    effortless and insightful.
                  </p>
                </div>
              </FadeInView>

              <StaggeredAnimation
                staggerDelay={0.15}
                className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {features.map((feature, index) => (
                  <HoverScale key={feature.title}>
                    <EnhancedTooltip
                      content={
                        <div className="space-y-2">
                          <p className="font-medium">{feature.title}</p>
                          <ul className="text-sm space-y-1">
                            {feature.benefits.map((benefit, i) => (
                              <li key={i} className="flex items-center gap-1">
                                <Star className="h-3 w-3" />
                                {benefit}
                              </li>
                            ))}
                          </ul>
                        </div>
                      }
                    >
                      <EnhancedCard variant="interactive" className="h-full">
                        <EnhancedCardHeader>
                          <div className="mb-4">{feature.icon}</div>
                          <EnhancedCardTitle className="text-xl">
                            {feature.title}
                          </EnhancedCardTitle>
                          <EnhancedCardDescription className="text-base">
                            {feature.description}
                          </EnhancedCardDescription>
                        </EnhancedCardHeader>
                      </EnhancedCard>
                    </EnhancedTooltip>
                  </HoverScale>
                ))}
              </StaggeredAnimation>
            </div>
          </section>

          {/* CTA Section */}
          <section className="py-20">
            <div className="container mx-auto px-4">
              <FadeInView>
                <EnhancedCard className="max-w-2xl mx-auto text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0">
                  <EnhancedCardContent className="py-12">
                    <h3 className="text-3xl font-bold mb-4">
                      Ready to transform your financial life?
                    </h3>
                    <p className="text-xl mb-8 text-blue-100">
                      Join thousands of users who have already taken control of
                      their finances.
                    </p>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <button
                        onClick={() => router.push("/auth/signup")}
                        className="bg-white text-blue-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg text-lg min-w-[200px] transition-colors duration-200"
                      >
                        Get Started Free
                      </button>

                      <button
                        onClick={() => router.push("/auth/signin")}
                        className="border-2 border-white text-white hover:bg-white/10 font-semibold py-3 px-8 rounded-lg text-lg min-w-[200px] transition-colors duration-200"
                      >
                        Sign In
                      </button>
                    </div>
                  </EnhancedCardContent>
                </EnhancedCard>
              </FadeInView>
            </div>
          </section>
        </div>
      </PageTransition>
    </ReducedMotion>
  );
}
