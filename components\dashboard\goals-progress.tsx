"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/lib/contexts/auth-context";
import { getUserGoals } from "@/lib/supabase/queries";
import { Goal } from "@/lib/types/database";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Target, Plus, TrendingUp } from "lucide-react";

export function GoalsProgress() {
  const { user, profile } = useAuth();
  const [goals, setGoals] = useState<Goal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const currency = profile?.currency_code || "USD";

  useEffect(() => {
    if (user) {
      loadGoals();
    }
  }, [user]);

  const loadGoals = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    try {
      const userGoals = await getUserGoals(user.id);
      // Filter to show only active goals and limit to top 4
      const activeGoals = userGoals
        .filter((goal) => goal.status === "active")
        .slice(0, 4);
      setGoals(activeGoals);
    } catch (error) {
      setError("Failed to load goals");
      // Set empty data on error
      setGoals([]);
    } finally {
      setLoading(false);
    }
  }, [user]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const getDaysUntilTarget = (targetDate: string | null) => {
    if (!targetDate) return null;
    const target = new Date(targetDate);
    const today = new Date();
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (loading) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>Financial Goals</CardTitle>
          <CardDescription>Loading your goals...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-2 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (goals.length === 0) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>Financial Goals</CardTitle>
          <CardDescription>Track your savings progress</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No goals set yet
            </h3>
            <p className="text-gray-600 mb-4">
              Create your first financial goal to start tracking progress
            </p>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Goal
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="@container/card">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Financial Goals</CardTitle>
            <CardDescription>
              Your progress towards financial milestones
            </CardDescription>
          </div>
          <Button variant="outline" size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Goal
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {goals.map((goal) => {
            const progress = calculateProgress(
              goal.current_amount,
              goal.target_amount,
            );
            const daysUntilTarget = getDaysUntilTarget(goal.target_date);
            const remaining = goal.target_amount - goal.current_amount;

            return (
              <div key={goal.id} className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-gray-900">{goal.name}</h4>
                    {goal.description && (
                      <p className="text-sm text-gray-600">
                        {goal.description}
                      </p>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-semibold text-gray-900">
                      {formatCurrency(goal.current_amount)} /{" "}
                      {formatCurrency(goal.target_amount)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {progress.toFixed(1)}% complete
                    </div>
                  </div>
                </div>

                <Progress value={progress} className="h-2" />

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-600">
                    <TrendingUp className="h-4 w-4 mr-1" />
                    {formatCurrency(remaining)} remaining
                  </div>
                  {daysUntilTarget !== null && (
                    <div className="text-gray-500">
                      {daysUntilTarget > 0
                        ? `${daysUntilTarget} days left`
                        : daysUntilTarget === 0
                          ? "Due today"
                          : `${Math.abs(daysUntilTarget)} days overdue`}
                    </div>
                  )}
                </div>
              </div>
            );
          })}

          {goals.length > 0 && (
            <div className="pt-4 border-t">
              <Button variant="ghost" className="w-full">
                View All Goals
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
