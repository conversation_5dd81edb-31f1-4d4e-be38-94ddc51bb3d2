"use client";

import { useEffect, useState, ReactNode } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/lib/contexts/auth-context";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Lock, Shield, AlertTriangle } from "lucide-react";

/**
 * Comprehensive authentication and authorization guards
 * Implements security best practices for route protection
 */

interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredRole?: "user" | "premium" | "admin";
  fallbackPath?: string;
  showFallback?: boolean;
}

interface RoleGuardProps {
  children: ReactNode;
  requiredRole: "user" | "premium" | "admin";
  fallback?: ReactNode;
}

interface ResourceGuardProps {
  children: ReactNode;
  resourceOwnerId: string;
  allowedRoles?: ("user" | "premium" | "admin")[];
  fallback?: ReactNode;
}

// Loading component for auth checks
const AuthLoadingScreen = () => (
  <div className="min-h-screen flex items-center justify-center">
    <Card className="w-full max-w-md">
      <CardContent className="pt-6">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <div className="text-center">
            <h3 className="text-lg font-semibold">Verifying Authentication</h3>
            <p className="text-sm text-muted-foreground">Please wait...</p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
);

// Unauthorized access component
const UnauthorizedScreen = ({
  title = "Access Denied",
  description = "You don't have permission to access this resource.",
  onSignIn,
  onGoBack,
}: {
  title?: string;
  description?: string;
  onSignIn?: () => void;
  onGoBack?: () => void;
}) => (
  <div className="min-h-screen flex items-center justify-center p-4">
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
          <Lock className="h-6 w-6 text-red-600" />
        </div>
        <CardTitle className="text-xl">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {onSignIn && (
          <Button onClick={onSignIn} className="w-full">
            Sign In
          </Button>
        )}
        {onGoBack && (
          <Button onClick={onGoBack} variant="outline" className="w-full">
            Go Back
          </Button>
        )}
      </CardContent>
    </Card>
  </div>
);

// Role hierarchy for permission checks
const ROLE_HIERARCHY = {
  user: 1,
  premium: 2,
  admin: 3,
} as const;

// Protected routes configuration
const PROTECTED_ROUTES = [
  "/dashboard",
  "/transactions",
  "/goals",
  "/loans-debts",
  "/recurring-payments",
  "/settings",
  "/ai-chat",
] as const;

const ADMIN_ROUTES = ["/admin", "/debug"] as const;

/**
 * Main authentication guard component
 * Protects routes based on authentication status and user roles
 */
export function AuthGuard({
  children,
  requireAuth = true,
  requiredRole = "user",
  fallbackPath = "/auth/signin",
  showFallback = true,
}: AuthGuardProps) {
  const { user, profile, loading, error } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      // Wait for auth to finish loading
      if (loading) return;

      setIsChecking(false);

      // Handle authentication errors
      if (error) {
        console.error("Auth error:", error);
        if (requireAuth && showFallback) {
          router.push(fallbackPath);
        }
        return;
      }

      // Check if authentication is required
      if (requireAuth && !user) {
        if (showFallback) {
          router.push(
            `${fallbackPath}?redirect=${encodeURIComponent(pathname)}`,
          );
        }
        return;
      }

      // Check role requirements
      if (user && profile && requiredRole) {
        const userRole = profile.role || "user";
        const hasRequiredRole =
          ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];

        if (!hasRequiredRole) {
          console.warn(
            `Access denied: User role '${userRole}' insufficient for required role '${requiredRole}'`,
          );
          if (showFallback) {
            router.push("/unauthorized");
          }
          return;
        }
      }
    };

    checkAuth();
  }, [
    user,
    profile,
    loading,
    error,
    requireAuth,
    requiredRole,
    router,
    pathname,
    fallbackPath,
    showFallback,
  ]);

  // Show loading screen while checking authentication
  if (loading || isChecking) {
    return <AuthLoadingScreen />;
  }

  // Show unauthorized screen if auth failed and showFallback is false
  if (requireAuth && !user && !showFallback) {
    return (
      <UnauthorizedScreen
        title="Authentication Required"
        description="Please sign in to access this page."
        onSignIn={() => router.push(fallbackPath)}
        onGoBack={() => router.back()}
      />
    );
  }

  // Check role authorization
  if (user && profile && requiredRole) {
    const userRole = profile.role || "user";
    const hasRequiredRole =
      ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];

    if (!hasRequiredRole && !showFallback) {
      return (
        <UnauthorizedScreen
          title="Insufficient Permissions"
          description={`This feature requires ${requiredRole} access or higher.`}
          onGoBack={() => router.back()}
        />
      );
    }
  }

  return <>{children}</>;
}

/**
 * Role-based guard for component-level protection
 */
export function RoleGuard({
  children,
  requiredRole,
  fallback,
}: RoleGuardProps) {
  const { user, profile } = useAuth();

  if (!user || !profile) {
    return (
      fallback || (
        <div className="text-center py-8">
          <Shield className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Authentication required</p>
        </div>
      )
    );
  }

  const userRole = profile.role || "user";
  const hasRequiredRole =
    ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];

  if (!hasRequiredRole) {
    return (
      fallback || (
        <div className="text-center py-8">
          <Lock className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Insufficient permissions</p>
          <p className="text-sm text-muted-foreground mt-2">
            Requires {requiredRole} access or higher
          </p>
        </div>
      )
    );
  }

  return <>{children}</>;
}

/**
 * Resource ownership guard
 * Ensures users can only access their own resources
 */
export function ResourceGuard({
  children,
  resourceOwnerId,
  allowedRoles = ["admin"],
  fallback,
}: ResourceGuardProps) {
  const { user, profile } = useAuth();

  if (!user || !profile) {
    return (
      fallback || (
        <div className="text-center py-8">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-yellow-500" />
          <p className="text-muted-foreground">Authentication required</p>
        </div>
      )
    );
  }

  const userRole = profile.role || "user";
  const isOwner = user.id === resourceOwnerId;
  const hasAllowedRole = allowedRoles.includes(
    userRole as "user" | "premium" | "admin",
  );

  if (!isOwner && !hasAllowedRole) {
    return (
      fallback || (
        <div className="text-center py-8">
          <Lock className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-muted-foreground">Access denied</p>
          <p className="text-sm text-muted-foreground mt-2">
            You can only access your own resources
          </p>
        </div>
      )
    );
  }

  return <>{children}</>;
}

/**
 * HOC for protecting pages with authentication
 */
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<AuthGuardProps, "children"> = {},
) {
  const WrappedComponent = (props: P) => (
    <AuthGuard {...options}>
      <Component {...props} />
    </AuthGuard>
  );

  WrappedComponent.displayName = `withAuthGuard(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Hook for checking permissions
 */
export function usePermissions() {
  const { user, profile } = useAuth();

  const checkPermission = (
    requiredRole: "user" | "premium" | "admin",
    resourceOwnerId?: string,
  ): boolean => {
    if (!user || !profile) return false;

    const userRole = profile.role || "user";
    const hasRole = ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];

    if (resourceOwnerId) {
      return user.id === resourceOwnerId || hasRole;
    }

    return hasRole;
  };

  const isAuthenticated = !!user;
  const userRole = profile?.role || "user";
  const isAdmin = userRole === "admin";
  const isPremium = ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY.premium;

  return {
    isAuthenticated,
    userRole,
    isAdmin,
    isPremium,
    checkPermission,
    canAccess: (route: string) => {
      if (
        !isAuthenticated &&
        PROTECTED_ROUTES.some((pr) => route.startsWith(pr))
      ) {
        return false;
      }
      if (ADMIN_ROUTES.some((ar) => route.startsWith(ar)) && !isAdmin) {
        return false;
      }
      return true;
    },
  };
}

/**
 * Route-level protection middleware
 */
export function createRouteGuard(options: {
  requireAuth?: boolean;
  requiredRole?: "user" | "premium" | "admin";
  allowedPaths?: string[];
  blockedPaths?: string[];
}) {
  return function RouteGuard({ children }: { children: ReactNode }) {
    const pathname = usePathname();
    const { canAccess } = usePermissions();

    // Check if path is explicitly allowed
    if (options.allowedPaths?.some((path) => pathname.startsWith(path))) {
      return <>{children}</>;
    }

    // Check if path is explicitly blocked
    if (options.blockedPaths?.some((path) => pathname.startsWith(path))) {
      return <UnauthorizedScreen />;
    }

    // Check general access permissions
    if (!canAccess(pathname)) {
      return <UnauthorizedScreen />;
    }

    return (
      <AuthGuard
        requireAuth={options.requireAuth}
        requiredRole={options.requiredRole}
      >
        {children}
      </AuthGuard>
    );
  };
}
