"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { useDashboardData } from "@/lib/hooks/use-dashboard-data";
import {
  Activity,
  Clock,
  Database,
  Zap,
  Download,
  Upload,
  Gauge,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";

interface PerformanceMetrics {
  bundleSize: number;
  loadTime: number;
  renderTime: number;
  apiCalls: number;
  cacheHits: number;
  memoryUsage: number;
}

interface BundleAnalysis {
  totalSize: number;
  jsSize: number;
  cssSize: number;
  imageSize: number;
  aiComponentsSize: number;
  lazyLoadedSize: number;
}

export default function PerformanceTestingPage() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [bundleAnalysis, setBundleAnalysis] = useState<BundleAnalysis | null>(
    null,
  );
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  const { data: dashboardData, loading, error, refetch } = useDashboardData();

  const addTestResult = (result: string) => {
    setTestResults((prev) => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${result}`,
    ]);
  };

  // Simulate performance metrics collection
  const collectPerformanceMetrics = useCallback(async () => {
    setIsAnalyzing(true);
    addTestResult("Starting performance analysis...");

    try {
      // Simulate collecting real performance metrics
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Mock performance data (in a real app, you'd collect actual metrics)
      const mockMetrics: PerformanceMetrics = {
        bundleSize: Math.floor(Math.random() * 500) + 800, // KB
        loadTime: Math.floor(Math.random() * 1000) + 500, // ms
        renderTime: Math.floor(Math.random() * 100) + 50, // ms
        apiCalls: Math.floor(Math.random() * 5) + 3,
        cacheHits: Math.floor(Math.random() * 8) + 2,
        memoryUsage: Math.floor(Math.random() * 50) + 20, // MB
      };

      const mockBundleAnalysis: BundleAnalysis = {
        totalSize: mockMetrics.bundleSize,
        jsSize: Math.floor(mockMetrics.bundleSize * 0.6),
        cssSize: Math.floor(mockMetrics.bundleSize * 0.1),
        imageSize: Math.floor(mockMetrics.bundleSize * 0.2),
        aiComponentsSize: Math.floor(mockMetrics.bundleSize * 0.05), // Should be small due to lazy loading
        lazyLoadedSize: Math.floor(mockMetrics.bundleSize * 0.3),
      };

      setMetrics(mockMetrics);
      setBundleAnalysis(mockBundleAnalysis);
      addTestResult("Performance analysis completed");
    } catch (error) {
      addTestResult("Performance analysis failed");
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  // Test dashboard data loading performance
  const testDashboardPerformance = async () => {
    addTestResult("Testing dashboard data loading...");
    const startTime = performance.now();

    try {
      await refetch();
      const endTime = performance.now();
      const loadTime = Math.round(endTime - startTime);
      addTestResult(`Dashboard data loaded in ${loadTime}ms`);
    } catch (error) {
      addTestResult("Dashboard data loading failed");
    }
  };

  // Test AI component lazy loading
  const testAIComponentLoading = async () => {
    addTestResult("Testing AI component lazy loading...");

    try {
      const startTime = performance.now();

      // Simulate loading AI components
      await import("@/components/ai-chat/financial-chat-interface");
      await import("@/components/ai-chat/receipt-processor");

      const endTime = performance.now();
      const loadTime = Math.round(endTime - startTime);
      addTestResult(`AI components loaded in ${loadTime}ms`);
    } catch (error) {
      addTestResult("AI component loading failed");
    }
  };

  // Analyze bundle size impact
  const analyzeBundleSize = () => {
    if (!bundleAnalysis) return;

    const recommendations = [];

    if (bundleAnalysis.aiComponentsSize > bundleAnalysis.totalSize * 0.1) {
      recommendations.push(
        "AI components are taking up significant bundle space - ensure lazy loading is working",
      );
    } else {
      recommendations.push("✅ AI components are properly lazy-loaded");
    }

    if (bundleAnalysis.jsSize > bundleAnalysis.totalSize * 0.7) {
      recommendations.push(
        "JavaScript bundle is large - consider code splitting",
      );
    } else {
      recommendations.push("✅ JavaScript bundle size is reasonable");
    }

    if (bundleAnalysis.lazyLoadedSize > bundleAnalysis.totalSize * 0.4) {
      recommendations.push("✅ Good amount of code is lazy-loaded");
    } else {
      recommendations.push("Consider lazy loading more components");
    }

    return recommendations;
  };

  useEffect(() => {
    collectPerformanceMetrics();
  }, [collectPerformanceMetrics]);

  const getPerformanceScore = () => {
    if (!metrics) return 0;

    let score = 100;

    // Deduct points for poor performance
    if (metrics.loadTime > 1000) score -= 20;
    if (metrics.renderTime > 100) score -= 15;
    if (metrics.bundleSize > 1000) score -= 15;
    if (metrics.apiCalls > 5) score -= 10;
    if (metrics.memoryUsage > 50) score -= 10;

    return Math.max(score, 0);
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  const performanceScore = getPerformanceScore();
  const recommendations = bundleAnalysis ? analyzeBundleSize() : [];

  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">Performance Testing</h1>
        <p className="text-gray-600">
          Monitor bundle size, loading performance, and optimization
          effectiveness.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Score */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gauge className="w-5 h-5" />
              Performance Score
            </CardTitle>
            <CardDescription>
              Overall performance rating based on key metrics
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <div
                className={`text-4xl font-bold ${getScoreColor(performanceScore)}`}
              >
                {performanceScore}/100
              </div>
              <Progress value={performanceScore} className="mt-2" />
            </div>

            <div className="space-y-2">
              {performanceScore >= 90 && (
                <Alert>
                  <CheckCircle className="w-4 h-4" />
                  <AlertDescription>
                    Excellent performance! Your optimizations are working well.
                  </AlertDescription>
                </Alert>
              )}

              {performanceScore < 70 && (
                <Alert variant="destructive">
                  <AlertTriangle className="w-4 h-4" />
                  <AlertDescription>
                    Performance needs improvement. Check the recommendations
                    below.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Performance Metrics
            </CardTitle>
            <CardDescription>
              Key performance indicators for the application
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isAnalyzing ? (
              <div className="space-y-4">
                <div className="text-center">
                  <Activity className="w-8 h-8 animate-spin mx-auto mb-2" />
                  <p>Analyzing performance...</p>
                </div>
                <Progress value={66} />
              </div>
            ) : metrics ? (
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <Download className="w-4 h-4" />
                    Bundle Size
                  </span>
                  <span className="font-mono">{metrics.bundleSize} KB</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    Load Time
                  </span>
                  <span className="font-mono">{metrics.loadTime} ms</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <Zap className="w-4 h-4" />
                    Render Time
                  </span>
                  <span className="font-mono">{metrics.renderTime} ms</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <Database className="w-4 h-4" />
                    API Calls
                  </span>
                  <span className="font-mono">{metrics.apiCalls}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <TrendingUp className="w-4 h-4" />
                    Cache Hits
                  </span>
                  <span className="font-mono">{metrics.cacheHits}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <Upload className="w-4 h-4" />
                    Memory Usage
                  </span>
                  <span className="font-mono">{metrics.memoryUsage} MB</span>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">No metrics available</p>
            )}
          </CardContent>
        </Card>

        {/* Bundle Analysis */}
        {bundleAnalysis && (
          <Card>
            <CardHeader>
              <CardTitle>Bundle Size Analysis</CardTitle>
              <CardDescription>
                Breakdown of bundle size by component type
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>JavaScript</span>
                    <span>{bundleAnalysis.jsSize} KB</span>
                  </div>
                  <Progress
                    value={
                      (bundleAnalysis.jsSize / bundleAnalysis.totalSize) * 100
                    }
                  />
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Images</span>
                    <span>{bundleAnalysis.imageSize} KB</span>
                  </div>
                  <Progress
                    value={
                      (bundleAnalysis.imageSize / bundleAnalysis.totalSize) *
                      100
                    }
                  />
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>CSS</span>
                    <span>{bundleAnalysis.cssSize} KB</span>
                  </div>
                  <Progress
                    value={
                      (bundleAnalysis.cssSize / bundleAnalysis.totalSize) * 100
                    }
                  />
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>AI Components (Lazy)</span>
                    <span>{bundleAnalysis.aiComponentsSize} KB</span>
                  </div>
                  <Progress
                    value={
                      (bundleAnalysis.aiComponentsSize /
                        bundleAnalysis.totalSize) *
                      100
                    }
                    className="h-2"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Performance Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Tests</CardTitle>
            <CardDescription>
              Run specific tests to measure optimization effectiveness
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              <Button
                onClick={collectPerformanceMetrics}
                disabled={isAnalyzing}
                className="w-full"
              >
                {isAnalyzing ? "Analyzing..." : "Refresh Performance Metrics"}
              </Button>

              <Button
                onClick={testDashboardPerformance}
                variant="outline"
                className="w-full"
              >
                Test Dashboard Loading
              </Button>

              <Button
                onClick={testAIComponentLoading}
                variant="outline"
                className="w-full"
              >
                Test AI Component Lazy Loading
              </Button>
            </div>

            <div className="text-sm text-gray-600">
              <p>
                <strong>Dashboard Status:</strong>{" "}
                {loading ? "Loading..." : error ? "Error" : "Loaded"}
              </p>
              {dashboardData && (
                <p>
                  <strong>Data Points:</strong>{" "}
                  {(dashboardData.transactions?.length || 0) +
                    (dashboardData.goals?.length || 0) +
                    (dashboardData.recurringPayments?.length || 0) +
                    (dashboardData.loans?.length || 0)}{" "}
                  items
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recommendations */}
        {recommendations && recommendations.length > 0 && (
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Performance Recommendations</CardTitle>
              <CardDescription>
                Suggestions to improve application performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {recommendations?.map((recommendation, index) => (
                  <div
                    key={index}
                    className={`flex items-start gap-2 p-2 rounded ${
                      recommendation.startsWith("✅")
                        ? "bg-green-50 text-green-700"
                        : "bg-yellow-50 text-yellow-700"
                    }`}
                  >
                    {recommendation.startsWith("✅") ? (
                      <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                    )}
                    <span className="text-sm">{recommendation}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Test Results */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Test Results Log</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <h4 className="font-medium">Performance Tests:</h4>
                <Button
                  onClick={() => setTestResults([])}
                  variant="outline"
                  size="sm"
                >
                  Clear Log
                </Button>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg max-h-40 overflow-y-auto">
                {testResults.length === 0 ? (
                  <p className="text-gray-500 text-sm">
                    No test results yet. Run tests above!
                  </p>
                ) : (
                  <div className="space-y-1">
                    {testResults.map((result, index) => (
                      <div key={index} className="text-sm font-mono">
                        {result}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
