import { render, screen } from "@testing-library/react";
import { SpendingCategoriesChart } from "@/components/dashboard/spending-categories-chart";
import { AuthProvider } from "@/lib/contexts/auth-context";

// Mock the auth context
const mockUser = {
  id: "test-user-id",
  email: "<EMAIL>",
  created_at: new Date().toISOString(),
};

const mockProfile = {
  id: "test-profile-id",
  user_id: "test-user-id",
  full_name: "Test User",
  email: "<EMAIL>",
  currency_code: "USD",
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

const mockAuthContext = {
  user: mockUser,
  profile: mockProfile,
  session: null,
  loading: false,
  isOnboardingComplete: true,
  signIn: jest.fn(),
  signOut: jest.fn(),
  signUp: jest.fn(),
  updateProfile: jest.fn(),
  refreshProfile: jest.fn(),
};

// Mock the auth context hook
jest.mock("@/lib/contexts/auth-context", () => ({
  ...jest.requireActual("@/lib/contexts/auth-context"),
  useAuth: () => mockAuthContext,
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <AuthProvider>{component}</AuthProvider>,
  );
};

// Mock Recharts components
jest.mock("recharts", () => ({
  Pie: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="pie-chart">{children}</div>
  ),
  PieChart: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="pie-chart-container">{children}</div>
  ),
}));

// Mock chart UI components
jest.mock("@/components/ui/chart", () => ({
  ChartConfig: {},
  ChartContainer: ({
    children,
    className,
  }: {
    children: React.ReactNode;
    className?: string;
  }) => (
    <div data-testid="chart-container" className={className}>
      {children}
    </div>
  ),
  ChartTooltip: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="chart-tooltip">{children}</div>
  ),
  ChartTooltipContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tooltip-content">{children}</div>
  ),
}));

describe("SpendingCategoriesChart", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset any timers
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe("Loading State", () => {
    it("displays loading state initially", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      expect(screen.getByText("Spending by Category")).toBeInTheDocument();
      expect(screen.getByText("Loading spending data...")).toBeInTheDocument();

      // Should show loading skeleton
      const loadingSkeleton = screen.getByRole("generic", { hidden: true });
      expect(loadingSkeleton).toHaveClass(
        "animate-pulse",
        "bg-gray-200",
        "rounded-full",
      );
    });

    it("transitions from loading to empty state", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      // Initially loading
      expect(screen.getByText("Loading spending data...")).toBeInTheDocument();

      // Fast-forward the timer
      jest.advanceTimersByTime(500);

      // Should now show empty state
      expect(
        screen.getByText("No spending data available"),
      ).toBeInTheDocument();
    });
  });

  describe("Empty State", () => {
    it("displays empty state when no data is available", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      // Fast-forward past loading
      jest.advanceTimersByTime(500);

      expect(screen.getByText("Spending by Category")).toBeInTheDocument();
      expect(
        screen.getByText("No spending data available"),
      ).toBeInTheDocument();
      expect(screen.getByText("No transactions yet")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Add some transactions to see your spending breakdown",
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          "Start tracking your expenses to see category breakdown",
        ),
      ).toBeInTheDocument();
    });

    it("shows dashed border circle in empty state", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      jest.advanceTimersByTime(500);

      const emptyStateContainer = screen.getByRole("generic", { hidden: true });
      expect(emptyStateContainer).toHaveClass(
        "border-2",
        "border-dashed",
        "border-gray-300",
        "rounded-full",
      );
    });
  });

  describe("Currency Formatting", () => {
    it("uses USD currency by default", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      jest.advanceTimersByTime(500);

      // In empty state, the currency formatter should be initialized
      // This is indirectly tested through the component not crashing
      expect(
        screen.getByText("No spending data available"),
      ).toBeInTheDocument();
    });

    it("uses profile currency when available", () => {
      const eurProfile = { ...mockProfile, currency_code: "EUR" };
      const mockAuthContextEUR = { ...mockAuthContext, profile: eurProfile };

      // Temporarily update the mock for this test
      const originalMock = jest.requireMock("@/lib/contexts/auth-context").useAuth;
      jest.requireMock("@/lib/contexts/auth-context").useAuth = () => mockAuthContextEUR;
      
      render(
        <AuthProvider>
          <SpendingCategoriesChart />
        </AuthProvider>,
      );
      
      // Restore original mock
      jest.requireMock("@/lib/contexts/auth-context").useAuth = originalMock;

      jest.advanceTimersByTime(500);

      // Component should render without errors with EUR currency
      expect(
        screen.getByText("No spending data available"),
      ).toBeInTheDocument();
    });

    it("handles KWD currency from profile", () => {
      const kwdProfile = { ...mockProfile, currency_code: "KWD" };
      const mockAuthContextKWD = { ...mockAuthContext, profile: kwdProfile };

      // Temporarily update the mock for this test
      const originalMock = jest.requireMock("@/lib/contexts/auth-context").useAuth;
      jest.requireMock("@/lib/contexts/auth-context").useAuth = () => mockAuthContextKWD;
      
      render(
        <AuthProvider>
          <SpendingCategoriesChart />
        </AuthProvider>,
      );
      
      // Restore original mock
      jest.requireMock("@/lib/contexts/auth-context").useAuth = originalMock;

      jest.advanceTimersByTime(500);

      // Component should render without errors with KWD currency
      expect(
        screen.getByText("No spending data available"),
      ).toBeInTheDocument();
    });
  });

  describe("Chart Configuration", () => {
    it("has proper chart configuration structure", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      jest.advanceTimersByTime(500);

      // The chart config should be properly defined
      // This is tested indirectly by ensuring the component doesn't crash
      expect(screen.getByText("Spending by Category")).toBeInTheDocument();
    });
  });

  describe("Component Structure", () => {
    it("renders with proper card structure", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      expect(screen.getByText("Spending by Category")).toBeInTheDocument();

      // Should have card structure with header, content, and footer
      const cardElements = screen.getAllByRole("generic");
      expect(cardElements.length).toBeGreaterThan(0);
    });

    it("maintains proper loading to empty state transition", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      // Should start with loading
      expect(screen.getByText("Loading spending data...")).toBeInTheDocument();
      expect(
        screen.queryByText("No spending data available"),
      ).not.toBeInTheDocument();

      // After timer, should show empty state
      jest.advanceTimersByTime(500);

      expect(
        screen.queryByText("Loading spending data..."),
      ).not.toBeInTheDocument();
      expect(
        screen.getByText("No spending data available"),
      ).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("has proper heading hierarchy", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      jest.advanceTimersByTime(500);

      expect(screen.getByText("Spending by Category")).toBeInTheDocument();
      expect(
        screen.getByText("No spending data available"),
      ).toBeInTheDocument();
    });

    it("provides meaningful empty state messaging", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      jest.advanceTimersByTime(500);

      expect(screen.getByText("No transactions yet")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Add some transactions to see your spending breakdown",
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          "Start tracking your expenses to see category breakdown",
        ),
      ).toBeInTheDocument();
    });

    it("has proper content structure for screen readers", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      jest.advanceTimersByTime(500);

      // Should have descriptive text that helps users understand what to do
      const helpText = screen.getByText(
        "Add some transactions to see your spending breakdown",
      );
      expect(helpText).toBeInTheDocument();

      const actionText = screen.getByText(
        "Start tracking your expenses to see category breakdown",
      );
      expect(actionText).toBeInTheDocument();
    });
  });

  describe("User Profile Integration", () => {
    it("handles missing profile gracefully", () => {
      const mockAuthContextNoProfile = {
        ...mockAuthContext,
        profile: null,
      };

      // Temporarily update the mock for this test
      const originalMock = jest.requireMock("@/lib/contexts/auth-context").useAuth;
      jest.requireMock("@/lib/contexts/auth-context").useAuth = () => mockAuthContextNoProfile;
      
      render(
        <AuthProvider>
          <SpendingCategoriesChart />
        </AuthProvider>,
      );
      
      // Restore original mock
      jest.requireMock("@/lib/contexts/auth-context").useAuth = originalMock;

      jest.advanceTimersByTime(500);

      // Should still render and default to KWD (as specified in component)
      expect(
        screen.getByText("No spending data available"),
      ).toBeInTheDocument();
    });

    it("handles undefined currency_code", () => {
      const profileNoCurrency = { ...mockProfile, currency_code: undefined };
      const mockAuthContextNoCurrency = {
        ...mockAuthContext,
        profile: profileNoCurrency,
      };

      // Temporarily update the mock for this test
      const originalMock = jest.requireMock("@/lib/contexts/auth-context").useAuth;
      jest.requireMock("@/lib/contexts/auth-context").useAuth = () => mockAuthContextNoCurrency;
      
      render(
        <AuthProvider>
          <SpendingCategoriesChart />
        </AuthProvider>,
      );
      
      // Restore original mock
      jest.requireMock("@/lib/contexts/auth-context").useAuth = originalMock;

      jest.advanceTimersByTime(500);

      // Should fall back to KWD default
      expect(
        screen.getByText("No spending data available"),
      ).toBeInTheDocument();
    });
  });

  describe("Performance", () => {
    it("uses proper loading delay for UX", () => {
      renderWithProviders(<SpendingCategoriesChart />);

      // Should show loading initially
      expect(screen.getByText("Loading spending data...")).toBeInTheDocument();

      // Should not transition immediately
      jest.advanceTimersByTime(100);
      expect(screen.getByText("Loading spending data...")).toBeInTheDocument();

      // Should transition after 500ms
      jest.advanceTimersByTime(400);
      expect(
        screen.getByText("No spending data available"),
      ).toBeInTheDocument();
    });

    it("cleans up timer on unmount", () => {
      const { unmount } = renderWithProviders(<SpendingCategoriesChart />);

      // Start with loading state
      expect(screen.getByText("Loading spending data...")).toBeInTheDocument();

      // Unmount before timer completes
      unmount();

      // Timer should be cleaned up (no errors or memory leaks)
      jest.advanceTimersByTime(500);
      // If component was not properly cleaned up, this would cause issues
    });
  });
});
