import { createBrowserClient } from "@supabase/ssr";
import { Database } from "@/lib/types/database";

// Get environment variables directly for client-side usage
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Validate environment variables
if (!supabaseUrl) {
  throw new Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");
}

if (!supabaseAnonKey) {
  throw new Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");
}

// Validate URL format
try {
  new URL(supabaseUrl);
} catch {
  throw new Error(`Invalid NEXT_PUBLIC_SUPABASE_URL format: ${supabaseUrl}`);
}

// Create the Supabase client
export const supabase = createBrowserClient<Database>(
  supabaseUrl,
  supabaseAnonKey,
);

// Test connection function
export const testSupabaseConnection = async () => {
  try {
    const { error } = await supabase
      .from("user_profiles")
      .select("count")
      .limit(1);
    if (error) {
      throw new Error(`Supabase connection test failed: ${error.message}`);
    }
    return true;
  } catch (error) {
    console.error("Supabase connection test failed:", error);
    return false;
  }
};
