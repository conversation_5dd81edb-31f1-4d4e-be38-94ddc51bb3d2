/**
 * Topology Analyzer - Evaluates project characteristics to determine optimal swarm topology
 * Supports hierarchical, mesh, ring, and star topologies with dynamic switching
 */

export interface ProjectCharacteristics {
  complexity: number; // 0-1 scale
  parallelizability: number; // 0-1 scale
  interdependencies: number; // 0-1 scale
  resourceRequirements: number; // 0-1 scale
  timeSensitivity: number; // 0-1 scale
  faultTolerance: number; // 0-1 scale
  coordinationOverhead: number; // 0-1 scale
  scalability: number; // 0-1 scale
}

export interface TopologyMetrics {
  throughput: number;
  latency: number;
  errorRate: number;
  resourceUtilization: number;
  coordinationEfficiency: number;
  adaptationSpeed: number;
}

export interface TopologyRecommendation {
  topology: TopologyType;
  confidence: number;
  reasoning: string[];
  alternativeOptions: Array<{
    topology: TopologyType;
    confidence: number;
    suitability: string;
  }>;
  transitionPlan?: TopologyTransitionPlan;
}

export type TopologyType = "hierarchical" | "mesh" | "ring" | "star" | "hybrid";

export interface TopologyTransitionPlan {
  phases: Array<{
    description: string;
    duration: number;
    actions: string[];
    validationCriteria: string[];
  }>;
  rollbackStrategy: {
    triggers: string[];
    actions: string[];
  };
  riskAssessment: {
    level: "low" | "medium" | "high";
    factors: string[];
    mitigation: string[];
  };
}

export class TopologyAnalyzer {
  private performanceHistory: Map<TopologyType, TopologyMetrics[]> = new Map();
  private adaptationThreshold = 0.2; // 20% improvement needed for switching
  private stabilityWindow = 10; // Number of measurements for stability assessment

  constructor() {
    // Initialize performance history for all topology types
    ["hierarchical", "mesh", "ring", "star", "hybrid"].forEach((topology) => {
      this.performanceHistory.set(topology as TopologyType, []);
    });
  }

  /**
   * Analyze project characteristics from various inputs
   */
  analyzeProjectCharacteristics(inputs: {
    taskDescription: string;
    codebaseSize?: number;
    teamSize?: number;
    existingArchitecture?: string;
    constraints?: string[];
    objectives?: string[];
  }): ProjectCharacteristics {
    const {
      taskDescription,
      codebaseSize = 1000,
      teamSize = 5,
      constraints = [],
      objectives = [],
    } = inputs;

    // Natural language processing for task complexity
    const complexityIndicators = [
      "complex",
      "complicated",
      "sophisticated",
      "advanced",
      "intricate",
      "multi-layered",
      "enterprise",
      "distributed",
      "microservices",
    ];
    const complexity =
      this.calculateTextComplexity(taskDescription, complexityIndicators) *
      Math.min(codebaseSize / 10000, 1) *
      Math.min(teamSize / 10, 1);

    // Parallelizability assessment
    const parallelIndicators = [
      "parallel",
      "concurrent",
      "independent",
      "modular",
      "component",
      "service",
      "api",
      "batch",
      "pipeline",
    ];
    const parallelizability = this.calculateTextComplexity(
      taskDescription,
      parallelIndicators,
    );

    // Interdependency analysis
    const dependencyIndicators = [
      "dependent",
      "coupled",
      "integrated",
      "connected",
      "linked",
      "shared",
      "common",
      "centralized",
      "coordinated",
    ];
    const interdependencies = this.calculateTextComplexity(
      taskDescription,
      dependencyIndicators,
    );

    // Resource requirements
    const resourceIndicators = [
      "memory",
      "cpu",
      "database",
      "storage",
      "network",
      "gpu",
      "intensive",
      "heavy",
      "large-scale",
      "high-performance",
    ];
    const resourceRequirements =
      this.calculateTextComplexity(taskDescription, resourceIndicators) *
      Math.min(codebaseSize / 5000, 1);

    // Time sensitivity
    const timeIndicators = [
      "urgent",
      "critical",
      "deadline",
      "asap",
      "immediate",
      "priority",
      "real-time",
      "fast",
      "quick",
      "responsive",
    ];
    const timeSensitivity = this.calculateTextComplexity(
      taskDescription,
      timeIndicators,
    );

    // Fault tolerance requirements
    const faultToleranceIndicators = [
      "reliable",
      "resilient",
      "fault-tolerant",
      "redundant",
      "backup",
      "failover",
      "recovery",
      "availability",
      "uptime",
    ];
    const faultTolerance =
      this.calculateTextComplexity(taskDescription, faultToleranceIndicators) +
      (constraints.some((c) => c.includes("reliability")) ? 0.3 : 0);

    // Coordination overhead assessment
    const coordinationOverhead = interdependencies * 0.7 + complexity * 0.3;

    // Scalability requirements
    const scalabilityIndicators = [
      "scale",
      "scalable",
      "growth",
      "expand",
      "elastic",
      "dynamic",
      "load",
      "traffic",
      "users",
      "volume",
    ];
    const scalability = this.calculateTextComplexity(
      taskDescription,
      scalabilityIndicators,
    );

    return {
      complexity: Math.min(complexity, 1),
      parallelizability: Math.min(parallelizability, 1),
      interdependencies: Math.min(interdependencies, 1),
      resourceRequirements: Math.min(resourceRequirements, 1),
      timeSensitivity: Math.min(timeSensitivity, 1),
      faultTolerance: Math.min(faultTolerance, 1),
      coordinationOverhead: Math.min(coordinationOverhead, 1),
      scalability: Math.min(scalability, 1),
    };
  }

  /**
   * Recommend optimal topology based on project characteristics
   */
  recommendTopology(
    characteristics: ProjectCharacteristics,
  ): TopologyRecommendation {
    const scores = this.calculateTopologyScores(characteristics);
    const topScores = Object.entries(scores)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3);

    const [bestTopology, bestScore] = topScores[0];
    const confidence = this.calculateConfidence(bestScore, scores);

    const reasoning = this.generateReasoning(
      bestTopology as TopologyType,
      characteristics,
    );

    const alternativeOptions = topScores.slice(1).map(([topology, score]) => ({
      topology: topology as TopologyType,
      confidence: score,
      suitability: this.generateSuitabilityExplanation(
        topology as TopologyType,
        characteristics,
      ),
    }));

    const recommendation: TopologyRecommendation = {
      topology: bestTopology as TopologyType,
      confidence,
      reasoning,
      alternativeOptions,
    };

    // Add transition plan if switching from current topology
    const currentTopology = this.getCurrentTopology();
    if (currentTopology && currentTopology !== bestTopology) {
      recommendation.transitionPlan = this.generateTransitionPlan(
        currentTopology,
        bestTopology as TopologyType,
        characteristics,
      );
    }

    return recommendation;
  }

  /**
   * Calculate topology suitability scores
   */
  private calculateTopologyScores(
    chars: ProjectCharacteristics,
  ): Record<TopologyType, number> {
    const scores: Record<TopologyType, number> = {
      hierarchical: 0,
      mesh: 0,
      ring: 0,
      star: 0,
      hybrid: 0,
    };

    // Hierarchical topology scoring
    scores.hierarchical =
      chars.complexity * 0.25 +
      chars.interdependencies * 0.25 +
      (1 - chars.faultTolerance) * 0.15 +
      chars.coordinationOverhead * 0.15 +
      (1 - chars.parallelizability) * 0.2;

    // Mesh topology scoring
    scores.mesh =
      chars.faultTolerance * 0.3 +
      chars.parallelizability * 0.25 +
      (1 - chars.coordinationOverhead) * 0.2 +
      chars.scalability * 0.15 +
      (1 - chars.timeSensitivity) * 0.1;

    // Ring topology scoring
    scores.ring =
      (1 - chars.interdependencies) * 0.25 +
      chars.resourceRequirements * 0.2 +
      (1 - chars.complexity) * 0.2 +
      chars.parallelizability * 0.5 * 0.15 + // Sequential preference
      chars.timeSensitivity * 0.2;

    // Star topology scoring
    scores.star =
      (1 - chars.complexity) * 0.3 +
      chars.timeSensitivity * 0.25 +
      (1 - chars.scalability) * 0.2 +
      chars.coordinationOverhead * 0.15 +
      (1 - chars.faultTolerance) * 0.1;

    // Hybrid topology scoring (for mixed requirements)
    const variability = this.calculateCharacteristicVariability(chars);
    scores.hybrid =
      variability * 0.4 +
      chars.complexity * 0.2 +
      chars.scalability * 0.2 +
      chars.faultTolerance * 0.2;

    return scores;
  }

  /**
   * Calculate confidence level for recommendation
   */
  private calculateConfidence(
    bestScore: number,
    allScores: Record<TopologyType, number>,
  ): number {
    const scores = Object.values(allScores);
    const secondBest = scores.sort((a, b) => b - a)[1];
    const gap = bestScore - secondBest;

    // Confidence increases with score gap and absolute score
    return Math.min((gap * 2 + bestScore) / 2, 0.95);
  }

  /**
   * Generate reasoning for topology recommendation
   */
  private generateReasoning(
    topology: TopologyType,
    chars: ProjectCharacteristics,
  ): string[] {
    const reasoning: string[] = [];

    switch (topology) {
      case "hierarchical":
        if (chars.complexity > 0.7)
          reasoning.push(
            "High task complexity requires centralized coordination",
          );
        if (chars.interdependencies > 0.6)
          reasoning.push(
            "Strong interdependencies benefit from hierarchical structure",
          );
        if (chars.coordinationOverhead > 0.5)
          reasoning.push(
            "Coordination overhead is manageable with clear hierarchy",
          );
        break;

      case "mesh":
        if (chars.faultTolerance > 0.7)
          reasoning.push(
            "High fault tolerance requirements favor mesh topology",
          );
        if (chars.parallelizability > 0.6)
          reasoning.push(
            "High parallelizability enables efficient mesh coordination",
          );
        if (chars.scalability > 0.6)
          reasoning.push(
            "Scalability requirements suit distributed mesh structure",
          );
        break;

      case "ring":
        if (chars.resourceRequirements > 0.6)
          reasoning.push(
            "Resource constraints favor sequential ring processing",
          );
        if (chars.interdependencies < 0.4)
          reasoning.push("Low interdependencies suit ring topology");
        if (chars.parallelizability < 0.5)
          reasoning.push(
            "Sequential processing requirements align with ring structure",
          );
        break;

      case "star":
        if (chars.complexity < 0.5)
          reasoning.push("Simple tasks are well-suited for star topology");
        if (chars.timeSensitivity > 0.7)
          reasoning.push(
            "Time-sensitive tasks benefit from centralized star coordination",
          );
        if (chars.scalability < 0.4)
          reasoning.push("Limited scalability requirements suit star topology");
        break;

      case "hybrid":
        reasoning.push(
          "Mixed requirements suggest hybrid approach combining multiple topologies",
        );
        if (this.calculateCharacteristicVariability(chars) > 0.6) {
          reasoning.push(
            "High variability in requirements necessitates adaptive hybrid topology",
          );
        }
        break;
    }

    return reasoning;
  }

  /**
   * Generate suitability explanation for alternative topologies
   */
  private generateSuitabilityExplanation(
    topology: TopologyType,
    chars: ProjectCharacteristics,
  ): string {
    switch (topology) {
      case "hierarchical":
        return `Suitable for coordination-heavy tasks (interdependencies: ${(chars.interdependencies * 100).toFixed(0)}%)`;
      case "mesh":
        return `Good for fault-tolerant distributed processing (fault tolerance: ${(chars.faultTolerance * 100).toFixed(0)}%)`;
      case "ring":
        return `Efficient for sequential processing with resource constraints (resources: ${(chars.resourceRequirements * 100).toFixed(0)}%)`;
      case "star":
        return `Simple and fast for straightforward tasks (complexity: ${(chars.complexity * 100).toFixed(0)}%)`;
      case "hybrid":
        return `Adaptive solution for mixed requirements (variability: ${(this.calculateCharacteristicVariability(chars) * 100).toFixed(0)}%)`;
      default:
        return "Alternative topology option";
    }
  }

  /**
   * Calculate variability in characteristics to determine hybrid suitability
   */
  private calculateCharacteristicVariability(
    chars: ProjectCharacteristics,
  ): number {
    const values = Object.values(chars);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance =
      values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) /
      values.length;
    return Math.sqrt(variance);
  }

  /**
   * Generate transition plan for topology switching
   */
  private generateTransitionPlan(
    from: TopologyType,
    to: TopologyType,
    chars: ProjectCharacteristics,
  ): TopologyTransitionPlan {
    const isComplexTransition =
      (from === "hierarchical" && to === "mesh") ||
      (from === "mesh" && to === "hierarchical");

    const phases = [
      {
        description: `Pre-migration analysis and preparation for ${from} to ${to} transition`,
        duration: isComplexTransition ? 5 : 3,
        actions: [
          "Collect performance baseline metrics",
          "Analyze agent capabilities and assignments",
          "Map task dependencies and communication patterns",
          "Prepare rollback strategy and checkpoints",
        ],
        validationCriteria: [
          "Baseline metrics collected successfully",
          "All agent assignments documented",
          "Dependency map validated",
          "Rollback strategy tested",
        ],
      },
      {
        description: `Gradual transition to ${to} topology`,
        duration: isComplexTransition ? 10 : 7,
        actions: [
          "Begin incremental topology reconfiguration",
          "Update agent communication protocols",
          "Monitor performance during transition",
          "Adjust coordination strategies as needed",
        ],
        validationCriteria: [
          "No significant performance degradation",
          "All agents successfully reconfigured",
          "Communication protocols functioning",
          "Error rates within acceptable limits",
        ],
      },
      {
        description: `Post-migration optimization and validation`,
        duration: 5,
        actions: [
          "Fine-tune new topology parameters",
          "Validate performance improvements",
          "Update monitoring and alerting",
          "Document lessons learned",
        ],
        validationCriteria: [
          "Performance meets or exceeds baseline",
          "All systems functioning normally",
          "Monitoring systems updated",
          "Documentation completed",
        ],
      },
    ];

    const rollbackStrategy = {
      triggers: [
        "Performance degradation > 25%",
        "Error rate increase > 15%",
        "Agent failure rate > 30%",
        "Critical system failures",
      ],
      actions: [
        "Immediately halt transition process",
        "Restore previous topology configuration",
        "Validate system stability",
        "Analyze failure causes and update strategy",
      ],
    };

    const riskLevel = isComplexTransition
      ? "high"
      : chars.complexity > 0.7
        ? "medium"
        : "low";

    const riskAssessment = {
      level: riskLevel as "low" | "medium" | "high",
      factors: [
        `Complexity level: ${(chars.complexity * 100).toFixed(0)}%`,
        `Interdependency level: ${(chars.interdependencies * 100).toFixed(0)}%`,
        `Time sensitivity: ${(chars.timeSensitivity * 100).toFixed(0)}%`,
        `Transition complexity: ${isComplexTransition ? "High" : "Medium"}`,
      ],
      mitigation: [
        "Gradual transition with rollback points",
        "Real-time performance monitoring",
        "Automated rollback triggers",
        "Comprehensive testing at each phase",
      ],
    };

    return { phases, rollbackStrategy, riskAssessment };
  }

  /**
   * Calculate text complexity based on indicator words
   */
  private calculateTextComplexity(text: string, indicators: string[]): number {
    const lowerText = text.toLowerCase();
    const matches = indicators.filter((indicator) =>
      lowerText.includes(indicator),
    ).length;
    const wordCount = text.split(/\s+/).length;

    // Base score from indicator matches
    const indicatorScore = Math.min(matches / indicators.length, 1);

    // Adjust for text length (longer descriptions often indicate complexity)
    const lengthFactor = Math.min(wordCount / 100, 1) * 0.3;

    return Math.min(indicatorScore + lengthFactor, 1);
  }

  /**
   * Get current topology (mock implementation - would integrate with actual system)
   */
  private getCurrentTopology(): TopologyType | null {
    // This would be implemented to check the current system state
    // For now, return null to indicate no current topology
    return null;
  }

  /**
   * Record performance metrics for a topology
   */
  recordPerformanceMetrics(
    topology: TopologyType,
    metrics: TopologyMetrics,
  ): void {
    const history = this.performanceHistory.get(topology) || [];
    history.push({
      ...metrics,
      // Add timestamp if not present
      timestamp: Date.now(),
    } as TopologyMetrics & { timestamp: number });

    // Keep only recent history within stability window
    if (history.length > this.stabilityWindow) {
      history.splice(0, history.length - this.stabilityWindow);
    }

    this.performanceHistory.set(topology, history);
  }

  /**
   * Evaluate if topology switch is beneficial based on performance history
   */
  evaluateTopologySwitch(
    currentTopology: TopologyType,
    targetTopology: TopologyType,
  ): {
    shouldSwitch: boolean;
    confidence: number;
    expectedImprovement: number;
    reasoning: string;
  } {
    const currentMetrics = this.performanceHistory.get(currentTopology) || [];
    const targetMetrics = this.performanceHistory.get(targetTopology) || [];

    if (currentMetrics.length < 3) {
      return {
        shouldSwitch: false,
        confidence: 0.1,
        expectedImprovement: 0,
        reasoning: "Insufficient performance history for current topology",
      };
    }

    const currentAverage = this.calculateAveragePerformance(currentMetrics);
    const targetPredicted = this.predictPerformance(
      targetTopology,
      targetMetrics,
    );

    const expectedImprovement =
      (targetPredicted - currentAverage) / currentAverage;
    const shouldSwitch = expectedImprovement > this.adaptationThreshold;

    const confidence = this.calculateSwitchConfidence(
      currentMetrics,
      targetMetrics,
      expectedImprovement,
    );

    const reasoning = shouldSwitch
      ? `Expected ${(expectedImprovement * 100).toFixed(1)}% performance improvement with ${targetTopology} topology`
      : `Current ${currentTopology} topology performing adequately (${(expectedImprovement * 100).toFixed(1)}% expected change)`;

    return {
      shouldSwitch,
      confidence,
      expectedImprovement,
      reasoning,
    };
  }

  /**
   * Calculate average performance score from metrics
   */
  private calculateAveragePerformance(metrics: TopologyMetrics[]): number {
    if (metrics.length === 0) return 0;

    const weights = {
      throughput: 0.3,
      latency: -0.25, // Negative because lower is better
      errorRate: -0.2, // Negative because lower is better
      resourceUtilization: 0.15,
      coordinationEfficiency: 0.15,
      adaptationSpeed: 0.15,
    };

    return (
      metrics.reduce((sum, metric) => {
        const score =
          metric.throughput * weights.throughput +
          (1 - metric.latency) * Math.abs(weights.latency) + // Invert latency
          (1 - metric.errorRate) * Math.abs(weights.errorRate) + // Invert error rate
          metric.resourceUtilization * weights.resourceUtilization +
          metric.coordinationEfficiency * weights.coordinationEfficiency +
          metric.adaptationSpeed * weights.adaptationSpeed;

        return sum + score;
      }, 0) / metrics.length
    );
  }

  /**
   * Predict performance for target topology
   */
  private predictPerformance(
    topology: TopologyType,
    historicalMetrics: TopologyMetrics[],
  ): number {
    if (historicalMetrics.length > 0) {
      return this.calculateAveragePerformance(historicalMetrics);
    }

    // Fallback to topology-specific baseline predictions
    const baselines: Record<TopologyType, number> = {
      hierarchical: 0.75,
      mesh: 0.8,
      ring: 0.7,
      star: 0.65,
      hybrid: 0.78,
    };

    return baselines[topology];
  }

  /**
   * Calculate confidence in topology switch recommendation
   */
  private calculateSwitchConfidence(
    currentMetrics: TopologyMetrics[],
    targetMetrics: TopologyMetrics[],
    expectedImprovement: number,
  ): number {
    let confidence = 0.5; // Base confidence

    // Increase confidence with more data
    confidence += Math.min(currentMetrics.length / 10, 0.2);
    confidence += Math.min(targetMetrics.length / 10, 0.1);

    // Increase confidence with larger expected improvements
    confidence += Math.min(Math.abs(expectedImprovement), 0.2);

    // Decrease confidence if current performance is highly variable
    const currentVariability = this.calculateMetricsVariability(currentMetrics);
    confidence -= currentVariability * 0.15;

    return Math.max(0.1, Math.min(confidence, 0.95));
  }

  /**
   * Calculate variability in performance metrics
   */
  private calculateMetricsVariability(metrics: TopologyMetrics[]): number {
    if (metrics.length < 2) return 0;

    const scores = metrics.map((m) => this.calculateAveragePerformance([m]));
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance =
      scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) /
      scores.length;

    return Math.sqrt(variance);
  }
}

// Export default instance
export const topologyAnalyzer = new TopologyAnalyzer();
