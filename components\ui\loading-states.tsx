"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Wallet } from "lucide-react";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg" | "xl";
  variant?: "default" | "dots" | "pulse" | "bounce";
  className?: string;
}

function LoadingSpinner({
  size = "md",
  variant = "default",
  className,
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
    xl: "h-12 w-12",
  };

  if (variant === "dots") {
    return (
      <div className={cn("flex space-x-1", className)}>
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={cn(
              "bg-primary rounded-full animate-bounce",
              size === "sm" && "h-2 w-2",
              size === "md" && "h-3 w-3",
              size === "lg" && "h-4 w-4",
              size === "xl" && "h-6 w-6",
            )}
            style={{ animationDelay: `${i * 0.1}s` }}
          />
        ))}
      </div>
    );
  }

  if (variant === "pulse") {
    return (
      <div
        className={cn(
          "bg-primary rounded-full animate-pulse",
          sizeClasses[size],
          className,
        )}
      />
    );
  }

  if (variant === "bounce") {
    return (
      <div
        className={cn(
          "bg-primary rounded-full animate-bounce",
          sizeClasses[size],
          className,
        )}
      />
    );
  }

  return (
    <div
      className={cn(
        "animate-spin rounded-full border-2 border-muted border-t-primary",
        sizeClasses[size],
        className,
      )}
    />
  );
}

interface BrandedSpinnerProps {
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  message?: string;
}

function BrandedSpinner({
  size = "md",
  className,
  message = "Loading...",
}: BrandedSpinnerProps) {
  const sizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8",
    lg: "h-12 w-12",
    xl: "h-16 w-16",
  };

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center space-y-3",
        className,
      )}
    >
      <div className="relative">
        <div
          className={cn(
            "animate-spin rounded-full border-2 border-muted border-t-primary",
            sizeClasses[size],
          )}
        />
        <Wallet
          className={cn(
            "absolute inset-0 m-auto text-primary",
            size === "sm" && "h-3 w-3",
            size === "md" && "h-4 w-4",
            size === "lg" && "h-6 w-6",
            size === "xl" && "h-8 w-8",
          )}
        />
      </div>
      {message && (
        <p className="text-sm text-muted-foreground animate-pulse">{message}</p>
      )}
    </div>
  );
}

interface PageLoadingProps {
  message?: string;
  className?: string;
}

function PageLoading({
  message = "Loading page...",
  className,
}: PageLoadingProps) {
  return (
    <div
      className={cn(
        "min-h-screen flex items-center justify-center bg-background",
        className,
      )}
    >
      <BrandedSpinner size="lg" message={message} />
    </div>
  );
}

interface InlineLoadingProps {
  message?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}

function InlineLoading({
  message = "Loading...",
  size = "sm",
  className,
}: InlineLoadingProps) {
  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <LoadingSpinner size={size} />
      <span className="text-sm text-muted-foreground">{message}</span>
    </div>
  );
}

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  className?: string;
  children?: React.ReactNode;
}

function LoadingOverlay({
  isVisible,
  message = "Loading...",
  className,
  children,
}: LoadingOverlayProps) {
  if (!isVisible) return <>{children}</>;

  return (
    <div className="relative">
      {children}
      <div
        className={cn(
          "absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg",
          className,
        )}
      >
        <BrandedSpinner message={message} />
      </div>
    </div>
  );
}

// Loading state for specific components
function TableLoading({ rows = 5 }: { rows?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex space-x-4 animate-pulse">
          <div className="h-4 bg-muted rounded w-1/4" />
          <div className="h-4 bg-muted rounded w-1/4" />
          <div className="h-4 bg-muted rounded w-1/4" />
          <div className="h-4 bg-muted rounded w-1/4" />
        </div>
      ))}
    </div>
  );
}

function CardLoading() {
  return (
    <div className="p-6 space-y-4 animate-pulse">
      <div className="h-6 bg-muted rounded w-3/4" />
      <div className="h-4 bg-muted rounded w-1/2" />
      <div className="space-y-2">
        <div className="h-3 bg-muted rounded w-full" />
        <div className="h-3 bg-muted rounded w-5/6" />
      </div>
    </div>
  );
}

export {
  LoadingSpinner,
  BrandedSpinner,
  PageLoading,
  InlineLoading,
  LoadingOverlay,
  TableLoading,
  CardLoading,
};
